const { executeQuery } = require('./config/database');

async function runMigration() {
    try {
        console.log('📄 Running migration...');

        // Add new status values to uploads table
        await executeQuery(`
            ALTER TABLE uploads MODIFY COLUMN status
            ENUM('uploaded', 'reading', 'read', 'processing', 'completed', 'failed')
            DEFAULT 'uploaded'
        `);
        console.log('✅ Updated uploads table status enum');

        // Create initial_readings table
        await executeQuery(`
            CREATE TABLE IF NOT EXISTS initial_readings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                upload_id INT NOT NULL,
                initial_summary JSON DEFAULT NULL,
                status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
                INDEX idx_upload_id (upload_id),
                INDEX idx_status (status)
            )
        `);
        console.log('✅ Created initial_readings table');

        // Add user_prompt field to analyses table
        await executeQuery(`
            ALTER TABLE analyses ADD COLUMN IF NOT EXISTS user_prompt TEXT DEFAULT NULL
        `);
        console.log('✅ Added user_prompt field to analyses table');

        console.log('✅ Migration completed successfully');

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
        process.exit(1);
    }
}

runMigration();
