-- Market Analysis Database Schema
-- Created for Market Trend Analysis System

-- Create database
CREATE DATABASE IF NOT EXISTS market_analysis;
USE market_analysis;

-- Table untuk menyimpan file upload
CREATE TABLE uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VA<PERSON>HA<PERSON>(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    filepath VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('uploaded', 'reading', 'read', 'processing', 'completed', 'failed') DEFAULT 'uploaded',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_upload_date (upload_date),
    INDEX idx_status (status)
);

-- Table untuk menyimpan initial reading/summary
CREATE TABLE initial_readings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    upload_id INT NOT NULL,
    initial_summary JSON DEFAULT NULL,
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_status (status)
);

-- Table untuk menyimpan hasil analisis
CREATE TABLE analyses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    upload_id INT NOT NULL,
    recommendation ENUM('BUY', 'SELL', 'HOLD') NOT NULL,
    confidence_score DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    stop_loss DECIMAL(10,5) DEFAULT NULL,
    take_profit DECIMAL(10,5) DEFAULT NULL,
    entry_price DECIMAL(10,5) DEFAULT NULL,
    risk_reward_ratio DECIMAL(5,2) DEFAULT NULL,
    
    -- Price Action Analysis Results
    price_action_signals JSON DEFAULT NULL,
    candlestick_patterns JSON DEFAULT NULL,
    support_resistance_levels JSON DEFAULT NULL,
    trendline_analysis JSON DEFAULT NULL,
    
    -- Smart Money Concept Results
    smc_signals JSON DEFAULT NULL,
    order_blocks JSON DEFAULT NULL,
    liquidity_pools JSON DEFAULT NULL,
    breaker_blocks JSON DEFAULT NULL,
    market_structure JSON DEFAULT NULL,
    
    -- Technical Indicators Results
    rsi_analysis JSON DEFAULT NULL,
    macd_analysis JSON DEFAULT NULL,
    moving_averages JSON DEFAULT NULL,
    fibonacci_levels JSON DEFAULT NULL,
    
    -- Analysis Metadata
    analysis_duration_ms INT DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    user_prompt TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_recommendation (recommendation),
    INDEX idx_confidence (confidence_score),
    INDEX idx_created_at (created_at)
);

-- Table untuk menyimpan data OCR yang diekstrak
CREATE TABLE ocr_data (
    id INT PRIMARY KEY AUTO_INCREMENT,
    upload_id INT NOT NULL,
    extracted_text TEXT DEFAULT NULL,
    
    -- Price Data yang diekstrak
    price_data JSON DEFAULT NULL,
    timeframe VARCHAR(50) DEFAULT NULL,
    symbol VARCHAR(20) DEFAULT NULL,
    
    -- Chart Metadata
    chart_type VARCHAR(50) DEFAULT NULL,
    x_axis_data JSON DEFAULT NULL,
    y_axis_data JSON DEFAULT NULL,
    
    -- OCR Processing Info
    ocr_confidence DECIMAL(5,2) DEFAULT NULL,
    preprocessing_applied JSON DEFAULT NULL,
    processing_time_ms INT DEFAULT NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_symbol (symbol),
    INDEX idx_timeframe (timeframe)
);

-- Table untuk menyimpan feedback user (untuk improvement)
CREATE TABLE user_feedback (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_id INT NOT NULL,
    feedback_type ENUM('accuracy', 'suggestion', 'bug_report') NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT DEFAULT NULL,
    user_ip VARCHAR(45) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (analysis_id) REFERENCES analyses(id) ON DELETE CASCADE,
    INDEX idx_analysis_id (analysis_id),
    INDEX idx_feedback_type (feedback_type),
    INDEX idx_rating (rating)
);

-- Table untuk menyimpan log sistem
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level ENUM('INFO', 'WARNING', 'ERROR', 'DEBUG') NOT NULL,
    message TEXT NOT NULL,
    context JSON DEFAULT NULL,
    upload_id INT DEFAULT NULL,
    analysis_id INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE SET NULL,
    FOREIGN KEY (analysis_id) REFERENCES analyses(id) ON DELETE SET NULL,
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    INDEX idx_upload_id (upload_id)
);

-- Create views untuk reporting
CREATE VIEW analysis_summary AS
SELECT 
    u.id as upload_id,
    u.filename,
    u.upload_date,
    a.recommendation,
    a.confidence_score,
    a.stop_loss,
    a.take_profit,
    a.risk_reward_ratio,
    o.symbol,
    o.timeframe,
    a.created_at as analysis_date
FROM uploads u
LEFT JOIN analyses a ON u.id = a.upload_id
LEFT JOIN ocr_data o ON u.id = o.upload_id
WHERE u.status = 'completed';

-- Create view untuk performance metrics
CREATE VIEW performance_metrics AS
SELECT 
    DATE(created_at) as analysis_date,
    COUNT(*) as total_analyses,
    AVG(confidence_score) as avg_confidence,
    AVG(analysis_duration_ms) as avg_processing_time,
    COUNT(CASE WHEN recommendation = 'BUY' THEN 1 END) as buy_signals,
    COUNT(CASE WHEN recommendation = 'SELL' THEN 1 END) as sell_signals,
    COUNT(CASE WHEN recommendation = 'HOLD' THEN 1 END) as hold_signals
FROM analyses
GROUP BY DATE(created_at)
ORDER BY analysis_date DESC;
