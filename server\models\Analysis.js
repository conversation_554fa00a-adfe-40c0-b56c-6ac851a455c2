const { executeQ<PERSON>y, findOne, insertR<PERSON>ord, updateRecord } = require('../config/database');

class Analysis {
    // Create new analysis record
    static async create(analysisData) {
        const data = {
            upload_id: analysisData.upload_id,
            recommendation: analysisData.recommendation,
            confidence_score: analysisData.confidence_score || 0,
            stop_loss: analysisData.stop_loss || null,
            take_profit: analysisData.take_profit || null,
            entry_price: analysisData.entry_price || null,
            risk_reward_ratio: analysisData.risk_reward_ratio || null,
            
            // JSON fields for detailed analysis
            price_action_signals: JSON.stringify(analysisData.price_action_signals || {}),
            candlestick_patterns: JSON.stringify(analysisData.candlestick_patterns || {}),
            support_resistance_levels: JSON.stringify(analysisData.support_resistance_levels || {}),
            trendline_analysis: JSON.stringify(analysisData.trendline_analysis || {}),
            
            smc_signals: JSON.stringify(analysisData.smc_signals || {}),
            order_blocks: JSON.stringify(analysisData.order_blocks || {}),
            liquidity_pools: JSON.stringify(analysisData.liquidity_pools || {}),
            breaker_blocks: JSON.stringify(analysisData.breaker_blocks || {}),
            market_structure: JSON.stringify(analysisData.market_structure || {}),
            
            rsi_analysis: JSON.stringify(analysisData.rsi_analysis || {}),
            macd_analysis: JSON.stringify(analysisData.macd_analysis || {}),
            moving_averages: JSON.stringify(analysisData.moving_averages || {}),
            fibonacci_levels: JSON.stringify(analysisData.fibonacci_levels || {}),
            
            analysis_duration_ms: analysisData.analysis_duration_ms || null,
            error_message: analysisData.error_message || null
        };
        
        return await insertRecord('analyses', data);
    }
    
    // Find analysis by ID
    static async findById(id) {
        const query = `
            SELECT a.*, u.filename, u.original_filename, u.upload_date
            FROM analyses a
            JOIN uploads u ON a.upload_id = u.id
            WHERE a.id = ?
        `;
        const result = await findOne(query, [id]);
        
        if (result.success && result.data) {
            // Parse JSON fields
            const jsonFields = [
                'price_action_signals', 'candlestick_patterns', 'support_resistance_levels', 
                'trendline_analysis', 'smc_signals', 'order_blocks', 'liquidity_pools',
                'breaker_blocks', 'market_structure', 'rsi_analysis', 'macd_analysis',
                'moving_averages', 'fibonacci_levels'
            ];
            
            jsonFields.forEach(field => {
                if (result.data[field]) {
                    try {
                        result.data[field] = JSON.parse(result.data[field]);
                    } catch (e) {
                        result.data[field] = {};
                    }
                }
            });
        }
        
        return result;
    }
    
    // Find analysis by upload ID
    static async findByUploadId(uploadId) {
        const query = 'SELECT * FROM analyses WHERE upload_id = ?';
        const result = await findOne(query, [uploadId]);
        
        if (result.success && result.data) {
            // Parse JSON fields (same as findById)
            const jsonFields = [
                'price_action_signals', 'candlestick_patterns', 'support_resistance_levels', 
                'trendline_analysis', 'smc_signals', 'order_blocks', 'liquidity_pools',
                'breaker_blocks', 'market_structure', 'rsi_analysis', 'macd_analysis',
                'moving_averages', 'fibonacci_levels'
            ];
            
            jsonFields.forEach(field => {
                if (result.data[field]) {
                    try {
                        result.data[field] = JSON.parse(result.data[field]);
                    } catch (e) {
                        result.data[field] = {};
                    }
                }
            });
        }
        
        return result;
    }

    // Save initial reading data
    static async saveInitialReading(readingData) {
        const data = {
            upload_id: readingData.upload_id,
            initial_summary: JSON.stringify(readingData.summary_data || {}),
            status: readingData.status || 'completed',
            created_at: readingData.created_at || new Date()
        };

        return await insertRecord('initial_readings', data);
    }

    // Find initial reading by upload ID
    static async findInitialReadingByUploadId(uploadId) {
        const query = 'SELECT * FROM initial_readings WHERE upload_id = ?';
        const result = await findOne(query, [uploadId]);

        if (result.success && result.data) {
            // MySQL automatically parses JSON fields, no need to parse again
            if (!result.data.initial_summary) {
                result.data.initial_summary = {};
            }
        }

        return result;
    }

    // Get all analyses with pagination
    static async getAll(page = 1, limit = 10) {
        const offset = (page - 1) * limit;
        const query = `
            SELECT a.id, a.upload_id, a.recommendation, a.confidence_score,
                   a.stop_loss, a.take_profit, a.risk_reward_ratio,
                   a.created_at, u.filename, u.original_filename
            FROM analyses a
            JOIN uploads u ON a.upload_id = u.id
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?
        `;
        return await executeQuery(query, [limit, offset]);
    }
    
    // Get analysis statistics
    static async getStats() {
        const queries = {
            total: 'SELECT COUNT(*) as count FROM analyses',
            buy_signals: 'SELECT COUNT(*) as count FROM analyses WHERE recommendation = "BUY"',
            sell_signals: 'SELECT COUNT(*) as count FROM analyses WHERE recommendation = "SELL"',
            hold_signals: 'SELECT COUNT(*) as count FROM analyses WHERE recommendation = "HOLD"',
            avg_confidence: 'SELECT AVG(confidence_score) as avg FROM analyses',
            today: 'SELECT COUNT(*) as count FROM analyses WHERE DATE(created_at) = CURDATE()'
        };
        
        const results = {};
        for (const [key, query] of Object.entries(queries)) {
            const result = await findOne(query);
            if (key === 'avg_confidence') {
                results[key] = result.success ? parseFloat(result.data.avg || 0).toFixed(2) : 0;
            } else {
                results[key] = result.success ? result.data.count : 0;
            }
        }
        
        return { success: true, data: results };
    }
    
    // Get performance metrics by date range
    static async getPerformanceMetrics(startDate, endDate) {
        const query = `
            SELECT 
                DATE(created_at) as analysis_date,
                COUNT(*) as total_analyses,
                AVG(confidence_score) as avg_confidence,
                AVG(analysis_duration_ms) as avg_processing_time,
                COUNT(CASE WHEN recommendation = 'BUY' THEN 1 END) as buy_signals,
                COUNT(CASE WHEN recommendation = 'SELL' THEN 1 END) as sell_signals,
                COUNT(CASE WHEN recommendation = 'HOLD' THEN 1 END) as hold_signals
            FROM analyses
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY DATE(created_at)
            ORDER BY analysis_date DESC
        `;
        return await executeQuery(query, [startDate, endDate]);
    }
}

module.exports = Analysis;
