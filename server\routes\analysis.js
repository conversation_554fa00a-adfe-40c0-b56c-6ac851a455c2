const express = require('express');
const { spawn } = require('child_process');
const path = require('path');

const { catchAsync, ValidationError, NotFoundError, AnalysisError } = require('../middleware/errorHandler');
const { customLogger } = require('../middleware/logger');
const Upload = require('../models/Upload');
const Analysis = require('../models/Analysis');
const OCRData = require('../models/OCRData');

const router = express.Router();

// POST /api/analysis/initial-read/:uploadId - Initial reading and summary of uploaded image
router.post('/initial-read/:uploadId', catchAsync(async (req, res) => {
    const { uploadId } = req.params;

    if (!uploadId || isNaN(uploadId)) {
        throw new ValidationError('Invalid upload ID');
    }

    // Check if upload exists
    const uploadResult = await Upload.findById(parseInt(uploadId));

    if (!uploadResult.success) {
        throw new Error(uploadResult.error);
    }

    if (!uploadResult.data) {
        throw new NotFoundError('Upload not found');
    }

    const upload = uploadResult.data;

    // Check if initial reading already exists
    const existingReading = await Analysis.findInitialReadingByUploadId(parseInt(uploadId));

    if (existingReading.success && existingReading.data) {
        return res.json({
            success: true,
            message: 'Initial reading already exists',
            data: existingReading.data
        });
    }

    // Update upload status to reading
    await Upload.updateStatus(parseInt(uploadId), 'reading');

    customLogger.info(`Starting initial reading for upload ${uploadId}`);

    try {
        // Start Python initial reading
        const readingResult = await startPythonInitialReading(upload);

        // Save initial reading to database
        const saveResult = await Analysis.saveInitialReading({
            upload_id: parseInt(uploadId),
            summary_data: readingResult,
            status: 'completed',
            created_at: new Date()
        });

        if (!saveResult.success) {
            throw new Error('Failed to save initial reading');
        }

        customLogger.success(`Initial reading completed for upload ${uploadId}`);
        await Upload.updateStatus(parseInt(uploadId), 'read');

        res.json({
            success: true,
            message: 'Initial reading completed',
            data: readingResult
        });

    } catch (error) {
        customLogger.error(`Initial reading failed for upload ${uploadId}`, { error: error.message });
        await Upload.updateStatus(parseInt(uploadId), 'failed');

        res.status(500).json({
            success: false,
            message: 'Initial reading failed',
            error: error.message
        });
    }
}));

// GET /api/analysis/initial-read/:uploadId - Get initial reading results (for polling)
router.get('/initial-read/:uploadId', catchAsync(async (req, res) => {
    const { uploadId } = req.params;

    if (!uploadId || isNaN(uploadId)) {
        throw new ValidationError('Invalid upload ID');
    }

    // Check if upload exists
    const uploadResult = await Upload.findById(parseInt(uploadId));

    if (!uploadResult.success) {
        throw new Error(uploadResult.error);
    }

    if (!uploadResult.data) {
        throw new NotFoundError('Upload not found');
    }

    // Check if initial reading exists
    const existingReading = await Analysis.findInitialReadingByUploadId(parseInt(uploadId));

    if (existingReading.success && existingReading.data) {
        return res.json({
            success: true,
            message: 'Initial reading found',
            data: existingReading.data.initial_summary
        });
    }

    // Check upload status
    const upload = uploadResult.data;
    if (upload.status === 'reading') {
        return res.json({
            success: false,
            status: 'processing',
            message: 'Initial reading in progress'
        });
    } else if (upload.status === 'uploaded') {
        return res.json({
            success: false,
            status: 'pending',
            message: 'Initial reading not started'
        });
    } else {
        return res.json({
            success: false,
            status: 'failed',
            message: 'Initial reading failed or not found'
        });
    }
}));

// POST /api/analysis/final-analyze/:uploadId - Final analysis with user context
router.post('/final-analyze/:uploadId', catchAsync(async (req, res) => {
    const { uploadId } = req.params;
    const { user_prompt, skip_confirmation } = req.body;
    
    if (!uploadId || isNaN(uploadId)) {
        throw new ValidationError('Invalid upload ID');
    }
    
    // Check if upload exists
    const uploadResult = await Upload.findById(parseInt(uploadId));
    
    if (!uploadResult.success) {
        throw new Error(uploadResult.error);
    }
    
    if (!uploadResult.data) {
        throw new NotFoundError('Upload not found');
    }
    
    const upload = uploadResult.data;
    
    // Check if analysis already exists
    const existingAnalysis = await Analysis.findByUploadId(parseInt(uploadId));
    
    if (existingAnalysis.success && existingAnalysis.data) {
        return res.json({
            success: true,
            message: 'Analysis already exists',
            data: {
                analysisId: existingAnalysis.data.id,
                status: 'completed'
            }
        });
    }
    
    // Update upload status to processing
    await Upload.updateStatus(parseInt(uploadId), 'processing');
    
    customLogger.info(`Starting final analysis for upload ${uploadId}`, { user_prompt: user_prompt || 'none' });

    // Start Python final analysis in background
    startPythonFinalAnalysis(upload, user_prompt)
        .then(async (analysisResult) => {
            customLogger.success(`Final analysis completed for upload ${uploadId}`);
            await Upload.updateStatus(parseInt(uploadId), 'completed');
        })
        .catch(async (error) => {
            customLogger.error(`Final analysis failed for upload ${uploadId}`, { error: error.message });
            await Upload.updateStatus(parseInt(uploadId), 'failed');
        });

    res.json({
        success: true,
        message: 'Final analysis started',
        data: {
            uploadId: parseInt(uploadId),
            status: 'processing',
            estimatedTime: '30-60 seconds',
            user_prompt: user_prompt || null
        }
    });
}));

// GET /api/analysis/:id - Get analysis results
router.get('/:id', catchAsync(async (req, res) => {
    const { id } = req.params;
    
    if (!id || isNaN(id)) {
        throw new ValidationError('Invalid analysis ID');
    }
    
    const result = await Analysis.findById(parseInt(id));
    
    if (!result.success) {
        throw new Error(result.error);
    }
    
    if (!result.data) {
        throw new NotFoundError('Analysis not found');
    }
    
    res.json({
        success: true,
        data: result.data
    });
}));

// GET /api/analysis/upload/:uploadId - Get analysis by upload ID
router.get('/upload/:uploadId', catchAsync(async (req, res) => {
    const { uploadId } = req.params;
    
    if (!uploadId || isNaN(uploadId)) {
        throw new ValidationError('Invalid upload ID');
    }
    
    const result = await Analysis.findByUploadId(parseInt(uploadId));
    
    if (!result.success) {
        throw new Error(result.error);
    }
    
    if (!result.data) {
        // Check upload status
        const uploadResult = await Upload.findById(parseInt(uploadId));
        
        if (!uploadResult.success || !uploadResult.data) {
            throw new NotFoundError('Upload not found');
        }
        
        return res.json({
            success: true,
            data: null,
            status: uploadResult.data.status,
            message: uploadResult.data.status === 'processing' 
                ? 'Analysis in progress' 
                : 'Analysis not started'
        });
    }
    
    res.json({
        success: true,
        data: result.data
    });
}));

// GET /api/analysis - Get all analyses with pagination
router.get('/', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    if (page < 1 || limit < 1 || limit > 100) {
        throw new ValidationError('Invalid pagination parameters');
    }
    
    const result = await Analysis.getAll(page, limit);
    
    if (!result.success) {
        throw new Error(result.error);
    }
    
    res.json({
        success: true,
        data: result.data
    });
}));

// Function to start Python analysis
async function startPythonAnalysis(upload) {
    return new Promise((resolve, reject) => {
        const pythonScript = path.join(__dirname, '../../python_analysis/main.py');
        const imagePath = path.resolve(upload.filepath);
        const uploadId = upload.id;

        customLogger.info(`Executing Python analysis: ${pythonScript} ${imagePath} ${uploadId}`);

        // Verify image file exists
        const fs = require('fs');
        if (!fs.existsSync(imagePath)) {
            reject(new AnalysisError(`Image file not found: ${imagePath}`));
            return;
        }

        // Spawn Python process
        const pythonProcess = spawn('python', [pythonScript, imagePath, uploadId.toString()], {
            cwd: path.join(__dirname, '../../python_analysis'),
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
            const chunk = data.toString();
            stdout += chunk;
            customLogger.info(`Python stdout: ${chunk.trim()}`);
        });

        pythonProcess.stderr.on('data', (data) => {
            const chunk = data.toString();
            stderr += chunk;
            customLogger.warn(`Python stderr: ${chunk.trim()}`);
        });

        pythonProcess.on('close', async (code) => {
            customLogger.info(`Python process exited with code ${code}`);

            if (code === 0 && stdout.trim()) {
                try {
                    // Find JSON in output (in case there are other log messages)
                    let analysisResult;
                    const jsonMatch = stdout.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        analysisResult = JSON.parse(jsonMatch[0]);
                    } else {
                        analysisResult = JSON.parse(stdout.trim());
                    }

                    customLogger.success('Python analysis completed successfully');

                    // Save OCR data
                    if (analysisResult.ocr_data) {
                        const ocrResult = await OCRData.create({
                            upload_id: uploadId,
                            extracted_text: analysisResult.ocr_data.text || '',
                            symbol: analysisResult.ocr_data.symbol || '',
                            timeframe: analysisResult.ocr_data.timeframe || '',
                            current_price: analysisResult.ocr_data.current_price || 0,
                            price_data: JSON.stringify(analysisResult.ocr_data.price_data || {}),
                            metadata: JSON.stringify(analysisResult.ocr_data.metadata || {})
                        });

                        if (!ocrResult.success) {
                            customLogger.error('Failed to save OCR data', { error: ocrResult.error });
                        }
                    }

                    // Save complete analysis results
                    const analysisData = {
                        upload_id: uploadId,
                        status: 'completed',
                        results: JSON.stringify(analysisResult),
                        recommendation: analysisResult.decision?.recommendation || 'HOLD',
                        confidence: analysisResult.decision?.confidence || 0.0,
                        entry_price: analysisResult.decision?.entry_price || 0.0,
                        stop_loss: analysisResult.decision?.stop_loss || 0.0,
                        take_profit: analysisResult.decision?.take_profit || 0.0,
                        risk_reward_ratio: analysisResult.decision?.risk_reward_ratio || 0.0,
                        completed_at: new Date()
                    };

                    const analysisResult_db = await Analysis.create(analysisData);

                    if (!analysisResult_db.success) {
                        customLogger.error('Failed to save analysis results', { error: analysisResult_db.error });
                        reject(new AnalysisError('Failed to save analysis results'));
                        return;
                    }

                    resolve(analysisResult);

                } catch (error) {
                    customLogger.error('Failed to parse Python output', {
                        stdout: stdout.substring(0, 500),
                        stderr: stderr.substring(0, 500),
                        error: error.message
                    });
                    reject(new AnalysisError('Failed to parse analysis results'));
                }
            } else {
                const errorMessage = stderr || 'Python analysis failed with no output';
                customLogger.error('Python analysis failed', {
                    code,
                    stdout: stdout.substring(0, 500),
                    stderr: stderr.substring(0, 500)
                });
                reject(new AnalysisError(`Python analysis failed: ${errorMessage}`));
            }
        });

        pythonProcess.on('error', (error) => {
            customLogger.error('Failed to start Python process', { error: error.message });
            reject(new AnalysisError('Failed to start analysis process'));
        });

        // Set timeout for analysis (5 minutes)
        const timeout = setTimeout(() => {
            pythonProcess.kill('SIGTERM');
            reject(new AnalysisError('Analysis timeout - process took longer than 5 minutes'));
        }, 5 * 60 * 1000);

        // Clear timeout when process completes
        pythonProcess.on('close', () => {
            clearTimeout(timeout);
        });
    });
}

// Function to start Python initial reading
async function startPythonInitialReading(upload) {
    return new Promise((resolve, reject) => {
        const pythonScript = path.join(__dirname, '../../python_analysis/main.py');
        const imagePath = path.resolve(upload.filepath);
        const uploadId = upload.id;

        customLogger.info(`Executing Python initial reading: ${pythonScript} ${imagePath} ${uploadId} summary`);

        // Verify image file exists
        const fs = require('fs');
        if (!fs.existsSync(imagePath)) {
            reject(new AnalysisError(`Image file not found: ${imagePath}`));
            return;
        }

        // Spawn Python process with summary mode
        const pythonProcess = spawn('python', [pythonScript, imagePath, uploadId.toString(), 'summary'], {
            cwd: path.join(__dirname, '../../python_analysis'),
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
            const chunk = data.toString();
            stdout += chunk;
            customLogger.info(`Python initial reading stdout: ${chunk.trim()}`);
        });

        pythonProcess.stderr.on('data', (data) => {
            const chunk = data.toString();
            stderr += chunk;
            customLogger.warn(`Python initial reading stderr: ${chunk.trim()}`);
        });

        pythonProcess.on('close', async (code) => {
            customLogger.info(`Python initial reading process exited with code ${code}`);

            if (code === 0 && stdout.trim()) {
                try {
                    // Find JSON in output
                    let readingResult;
                    const jsonMatch = stdout.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        readingResult = JSON.parse(jsonMatch[0]);
                    } else {
                        readingResult = JSON.parse(stdout.trim());
                    }

                    if (readingResult.success) {
                        resolve(readingResult);
                    } else {
                        reject(new AnalysisError(`Initial reading failed: ${readingResult.error}`));
                    }
                } catch (parseError) {
                    customLogger.error('Failed to parse initial reading JSON output', {
                        error: parseError.message,
                        stdout: stdout.substring(0, 500)
                    });
                    reject(new AnalysisError('Failed to parse initial reading results'));
                }
            } else {
                const errorMessage = stderr.trim() || `Process exited with code ${code}`;
                customLogger.error('Initial reading process failed', {
                    code,
                    stderr: stderr.substring(0, 500),
                    stdout: stdout.substring(0, 500)
                });
                reject(new AnalysisError(`Initial reading failed: ${errorMessage}`));
            }
        });

        pythonProcess.on('error', (error) => {
            customLogger.error('Failed to start Python initial reading process', { error: error.message });
            reject(new AnalysisError('Failed to start initial reading process'));
        });

        // Set timeout for initial reading (2 minutes)
        const timeout = setTimeout(() => {
            pythonProcess.kill('SIGTERM');
            reject(new AnalysisError('Initial reading timeout - process took longer than 2 minutes'));
        }, 2 * 60 * 1000);

        // Clear timeout when process completes
        pythonProcess.on('close', () => {
            clearTimeout(timeout);
        });
    });
}

// Function to start Python final analysis with user context
async function startPythonFinalAnalysis(upload, userPrompt = null) {
    return new Promise((resolve, reject) => {
        const pythonScript = path.join(__dirname, '../../python_analysis/main.py');
        const imagePath = path.resolve(upload.filepath);
        const uploadId = upload.id;

        // Prepare arguments
        const args = [pythonScript, imagePath, uploadId.toString(), 'full'];
        if (userPrompt) {
            args.push(userPrompt);
        }

        customLogger.info(`Executing Python final analysis: ${args.join(' ')}`);

        // Verify image file exists
        const fs = require('fs');
        if (!fs.existsSync(imagePath)) {
            reject(new AnalysisError(`Image file not found: ${imagePath}`));
            return;
        }

        // Spawn Python process with full analysis mode
        const pythonProcess = spawn('python', args, {
            cwd: path.join(__dirname, '../../python_analysis'),
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
            const chunk = data.toString();
            stdout += chunk;
            customLogger.info(`Python final analysis stdout: ${chunk.trim()}`);
        });

        pythonProcess.stderr.on('data', (data) => {
            const chunk = data.toString();
            stderr += chunk;
            customLogger.warn(`Python final analysis stderr: ${chunk.trim()}`);
        });

        pythonProcess.on('close', async (code) => {
            customLogger.info(`Python final analysis process exited with code ${code}`);

            if (code === 0 && stdout.trim()) {
                try {
                    // Find JSON in output
                    let analysisResult;
                    const jsonMatch = stdout.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        analysisResult = JSON.parse(jsonMatch[0]);
                    } else {
                        analysisResult = JSON.parse(stdout.trim());
                    }

                    if (analysisResult.success) {
                        // Save analysis to database
                        const analysis = analysisResult.analysis;
                        const saveResult = await Analysis.create({
                            upload_id: parseInt(uploadId),
                            recommendation: analysis.recommendation,
                            confidence_score: analysis.confidence_score,
                            stop_loss: analysis.stop_loss,
                            take_profit: analysis.take_profit,
                            entry_price: analysis.entry_price,
                            risk_reward_ratio: analysis.risk_reward_ratio,

                            // JSON fields for detailed analysis
                            price_action_signals: analysis.price_action_signals || {},
                            candlestick_patterns: analysis.candlestick_patterns || {},
                            support_resistance_levels: analysis.support_resistance_levels || {},
                            trendline_analysis: analysis.trendline_analysis || {},

                            smc_signals: analysis.smc_signals || {},
                            order_blocks: analysis.order_blocks || {},
                            liquidity_pools: analysis.liquidity_pools || {},
                            breaker_blocks: analysis.breaker_blocks || {},
                            market_structure: analysis.market_structure || {},

                            rsi_analysis: analysis.rsi_analysis || {},
                            macd_analysis: analysis.macd_analysis || {},
                            moving_averages: analysis.moving_averages || {},
                            fibonacci_levels: analysis.fibonacci_levels || {},

                            analysis_duration_ms: analysisResult.analysis_duration_ms,
                            error_message: null
                        });

                        if (saveResult.success) {
                            resolve(analysisResult);
                        } else {
                            reject(new AnalysisError('Failed to save analysis results'));
                        }
                    } else {
                        reject(new AnalysisError(`Final analysis failed: ${analysisResult.error}`));
                    }
                } catch (parseError) {
                    customLogger.error('Failed to parse final analysis JSON output', {
                        error: parseError.message,
                        stdout: stdout.substring(0, 500)
                    });
                    reject(new AnalysisError('Failed to parse final analysis results'));
                }
            } else {
                const errorMessage = stderr.trim() || `Process exited with code ${code}`;
                customLogger.error('Final analysis process failed', {
                    code,
                    stderr: stderr.substring(0, 500),
                    stdout: stdout.substring(0, 500)
                });
                reject(new AnalysisError(`Final analysis failed: ${errorMessage}`));
            }
        });

        pythonProcess.on('error', (error) => {
            customLogger.error('Failed to start Python final analysis process', { error: error.message });
            reject(new AnalysisError('Failed to start final analysis process'));
        });

        // Set timeout for final analysis (5 minutes)
        const timeout = setTimeout(() => {
            pythonProcess.kill('SIGTERM');
            reject(new AnalysisError('Final analysis timeout - process took longer than 5 minutes'));
        }, 5 * 60 * 1000);

        // Clear timeout when process completes
        pythonProcess.on('close', () => {
            clearTimeout(timeout);
        });
    });
}

module.exports = router;
