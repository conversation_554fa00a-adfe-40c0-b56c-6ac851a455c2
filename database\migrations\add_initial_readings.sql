-- Migration: Add initial_readings table and user_prompt field
-- Date: 2025-01-26

USE market_analysis;

-- Add new status values to uploads table
ALTER TABLE uploads MODIFY COLUMN status ENUM('uploaded', 'reading', 'read', 'processing', 'completed', 'failed') DEFAULT 'uploaded';

-- Create initial_readings table
CREATE TABLE IF NOT EXISTS initial_readings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    upload_id INT NOT NULL,
    initial_summary JSON DEFAULT NULL,
    status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
    INDEX idx_upload_id (upload_id),
    INDEX idx_status (status)
);

-- Add user_prompt field to analyses table
ALTER TABLE analyses ADD COLUMN user_prompt TEXT DEFAULT NULL AFTER error_message;

-- Update existing analyses to have null user_prompt
UPDATE analyses SET user_prompt = NULL WHERE user_prompt IS NULL;

SELECT 'Migration completed successfully' as status;
