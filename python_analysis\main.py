#!/usr/bin/env python3
"""
Market Trend Analysis Engine
Main entry point for analyzing trading chart images
"""

import sys
import json
import os
import traceback
from datetime import datetime
import logging
import numpy as np

# Import analysis modules
from modules.image_processor import ImageProcessor
from modules.ocr_extractor import OCRExtractor
from modules.price_action_analyzer import PriceActionAnalyzer
from modules.smc_analyzer import SMCAnalyzer
from modules.technical_indicators import TechnicalIndicators
from modules.decision_engine import EnhancedDecisionEngine
from modules.advanced_signal_analyzer import AdvancedSignalAnalyzer

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder for numpy types"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

class MarketAnalysisEngine:
    """Main analysis engine that coordinates all analysis modules"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.ocr_extractor = OCRExtractor()
        self.price_action_analyzer = PriceActionAnalyzer()
        self.smc_analyzer = SMCAnalyzer()
        self.technical_indicators = TechnicalIndicators()
        self.decision_engine = EnhancedDecisionEngine()
        self.advanced_signal_analyzer = AdvancedSignalAnalyzer()
        
    def generate_image_summary(self, image_path, upload_id):
        """
        Generate initial summary of chart image for user confirmation

        Args:
            image_path (str): Path to the chart image
            upload_id (str): Upload ID from database

        Returns:
            dict: Summary results for user confirmation
        """
        try:
            logger.info(f"Generating summary for image: {image_path}")

            # Step 1: Process and prepare image
            logger.info("Step 1: Processing image...")
            processed_image = self.image_processor.process_image(image_path)

            if processed_image is None:
                raise Exception("Failed to process image")

            # Step 2: Extract text and price data using OCR
            logger.info("Step 2: Extracting OCR data...")
            ocr_data = self.ocr_extractor.extract_data(processed_image)

            # Step 3: Basic pattern recognition for summary
            logger.info("Step 3: Basic pattern recognition...")
            basic_patterns = self._identify_basic_patterns(processed_image, ocr_data)

            # Generate human-readable summary
            summary = self._generate_readable_summary(ocr_data, basic_patterns)

            # Compile summary results
            results = {
                "success": True,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "mode": "summary",
                "summary": summary,
                "ocr_data": {
                    "symbol": ocr_data.get("symbol", "UNKNOWN"),
                    "timeframe": ocr_data.get("timeframe", "UNKNOWN"),
                    "current_price": ocr_data.get("current_price", 0.0),
                    "extracted_text": ocr_data.get("text", ""),
                    "price_count": len(ocr_data.get("price_data", {}))
                },
                "basic_patterns": basic_patterns
            }

            logger.info("Summary generation completed successfully")
            return results

        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return {
                "success": False,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "mode": "summary",
                "error": str(e)
            }

    def analyze_chart(self, image_path, upload_id, user_prompt=None):
        """
        Main analysis function that processes a chart image

        Args:
            image_path (str): Path to the chart image
            upload_id (str): Upload ID from database
            user_prompt (str): Optional user correction/context

        Returns:
            dict: Complete analysis results
        """
        try:
            logger.info(f"Starting analysis for image: {image_path}")
            
            # Step 1: Process and prepare image
            logger.info("Step 1: Processing image...")
            processed_image = self.image_processor.process_image(image_path)
            
            if processed_image is None:
                raise Exception("Failed to process image")
            
            # Step 2: Extract text and price data using OCR
            logger.info("Step 2: Extracting OCR data...")
            ocr_data = self.ocr_extractor.extract_data(processed_image)
            
            # Step 3: Analyze price action patterns
            logger.info("Step 3: Analyzing price action...")
            price_action_signals = self.price_action_analyzer.analyze(processed_image, ocr_data)
            
            # Step 4: Apply Smart Money Concept analysis
            logger.info("Step 4: Analyzing Smart Money Concepts...")
            smc_signals = self.smc_analyzer.analyze(processed_image, ocr_data)
            
            # Step 5: Calculate technical indicators
            logger.info("Step 5: Calculating technical indicators...")
            technical_data = self.technical_indicators.calculate(processed_image, ocr_data)

            # Step 6: Advanced signal analysis
            logger.info("Step 6: Performing advanced signal analysis...")
            advanced_analysis = self.advanced_signal_analyzer.analyze_advanced_signals(
                processed_image, price_action_signals, smc_signals, technical_data, ocr_data
            )

            # Step 7: Apply user context if provided
            if user_prompt:
                logger.info("Step 7a: Processing user context...")
                ocr_data = self._apply_user_context(ocr_data, user_prompt)

            # Step 7: Make enhanced trading decision
            logger.info("Step 7: Making enhanced trading decision...")
            decision = self.decision_engine.make_decision(
                price_action_signals,
                smc_signals,
                technical_data,
                ocr_data,
                user_context=user_prompt
            )
            
            # Compile results
            results = {
                "success": True,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "mode": "full",
                "user_prompt": user_prompt,
                "ocr_data": {
                    "extracted_text": ocr_data.get("text", ""),
                    "symbol": ocr_data.get("symbol", "UNKNOWN"),
                    "timeframe": ocr_data.get("timeframe", "UNKNOWN"),
                    "current_price": ocr_data.get("current_price", 0.0),
                    "price_data": ocr_data.get("price_data", {}),
                    "chart_metadata": ocr_data.get("metadata", {})
                },
                "analysis": {
                    "recommendation": decision["recommendation"],
                    "confidence_score": decision["confidence"],
                    "confidence_level": decision.get("confidence_level", "unknown"),
                    "confidence_description": decision.get("confidence_description", ""),
                    "entry_price": decision["entry_price"],
                    "stop_loss": decision["stop_loss"],
                    "take_profit": decision["take_profit"],
                    "risk_reward_ratio": decision["risk_reward_ratio"],
                    "position_size": decision.get("position_size", 0.0),
                    "market_regime": decision.get("market_regime", "unknown"),

                    # Enhanced analysis results
                    "signal_reasoning": decision.get("signal_reasoning", {}),
                    "confluence_analysis": decision.get("confluence_analysis", {}),
                    "execution_plan": decision.get("execution_plan", {}),
                    "risk_factors": decision.get("risk_factors", []),
                    "supporting_factors": decision.get("supporting_factors", []),
                    "key_levels": decision.get("key_levels", []),
                    "timeframe_analysis": decision.get("timeframe_analysis", {}),
                    "scenario_analysis": decision.get("scenario_analysis", {}),

                    # Advanced signal analysis
                    "advanced_patterns": advanced_analysis.get("advanced_patterns", []),
                    "multi_timeframe_analysis": advanced_analysis.get("multi_timeframe_analysis", {}),
                    "signal_quality_metrics": advanced_analysis.get("signal_quality_metrics", {}),
                    "analysis_score": advanced_analysis.get("analysis_score", {}),
                    "recommendation_confidence": advanced_analysis.get("recommendation_confidence", {}),
                    "risk_assessment": advanced_analysis.get("risk_assessment", {}),
                    "execution_timing": advanced_analysis.get("execution_timing", {}),

                    # Original analysis data
                    "price_action_signals": price_action_signals,
                    "smc_signals": smc_signals,
                    "rsi_analysis": technical_data.get("rsi", {}),
                    "macd_analysis": technical_data.get("macd", {}),
                    "moving_averages": technical_data.get("moving_averages", {}),
                    "support_resistance": technical_data.get("support_resistance", {}),
                    "analysis_notes": decision.get("notes", ""),
                    "market_structure": smc_signals.get("market_structure", ""),
                    "trend_direction": price_action_signals.get("trend", "NEUTRAL"),

                    # Metadata
                    "analysis_version": "enhanced_v2.0",
                    "enhanced_features": [
                        "Advanced pattern recognition",
                        "Multi-timeframe analysis simulation",
                        "Signal confluence analysis",
                        "Enhanced risk management",
                        "Detailed signal reasoning",
                        "Market regime detection"
                    ]
                }
            }
            
            logger.info("Analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            logger.error(traceback.format_exc())
            
            return {
                "success": False,
                "upload_id": upload_id,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "ocr_data": None,
                "analysis": None
            }

    def _apply_user_context(self, ocr_data, user_prompt):
        """
        Apply user corrections/context to OCR data

        Args:
            ocr_data: Original OCR data
            user_prompt: User correction text

        Returns:
            dict: Updated OCR data with user context
        """
        try:
            logger.info(f"Applying user context: {user_prompt}")

            # Create a copy of OCR data
            updated_ocr = ocr_data.copy()

            # Parse user prompt for common corrections
            prompt_lower = user_prompt.lower()

            # Extract symbol corrections
            if "symbol" in prompt_lower or "pair" in prompt_lower:
                # Look for common currency pairs or symbols
                symbols = ["eurusd", "gbpusd", "usdjpy", "audusd", "usdcad", "nzdusd",
                          "xauusd", "xagusd", "btcusd", "ethusd"]
                for symbol in symbols:
                    if symbol in prompt_lower:
                        updated_ocr["symbol"] = symbol.upper()
                        logger.info(f"Updated symbol to: {symbol.upper()}")
                        break

            # Extract timeframe corrections
            timeframes = ["m1", "m5", "m15", "m30", "h1", "h4", "d1", "w1", "mn1"]
            for tf in timeframes:
                if tf in prompt_lower:
                    updated_ocr["timeframe"] = tf.upper()
                    logger.info(f"Updated timeframe to: {tf.upper()}")
                    break

            # Extract price corrections
            import re
            price_matches = re.findall(r'\d+\.?\d*', user_prompt)
            if price_matches:
                try:
                    # Use the first price found as current price
                    updated_ocr["current_price"] = float(price_matches[0])
                    logger.info(f"Updated current price to: {price_matches[0]}")
                except ValueError:
                    pass

            # Extract pattern/trend information
            if "bullish" in prompt_lower or "uptrend" in prompt_lower:
                updated_ocr["user_trend"] = "BULLISH"
            elif "bearish" in prompt_lower or "downtrend" in prompt_lower:
                updated_ocr["user_trend"] = "BEARISH"
            elif "sideways" in prompt_lower or "consolidation" in prompt_lower:
                updated_ocr["user_trend"] = "SIDEWAYS"

            # Extract pattern information
            patterns = ["double top", "double bottom", "head and shoulders", "triangle",
                       "flag", "pennant", "wedge", "channel"]
            for pattern in patterns:
                if pattern in prompt_lower:
                    updated_ocr["user_pattern"] = pattern.title()
                    logger.info(f"User identified pattern: {pattern.title()}")
                    break

            # Store original user prompt for reference
            updated_ocr["user_context"] = user_prompt

            return updated_ocr

        except Exception as e:
            logger.error(f"Error applying user context: {str(e)}")
            # Return original data if processing fails
            ocr_data["user_context"] = user_prompt
            return ocr_data

    def _identify_basic_patterns(self, processed_image, ocr_data):
        """
        Identify basic chart patterns for summary

        Args:
            processed_image: Processed image data
            ocr_data: OCR extracted data

        Returns:
            dict: Basic pattern information
        """
        try:
            # Basic trend analysis
            price_data = ocr_data.get("price_data", {})
            if not price_data:
                return {"trend": "Unknown", "pattern": "Insufficient data"}

            prices = list(price_data.values())
            if len(prices) < 3:
                return {"trend": "Unknown", "pattern": "Insufficient price data"}

            # Simple trend detection
            recent_prices = prices[-5:] if len(prices) >= 5 else prices
            if len(recent_prices) >= 2:
                if recent_prices[-1] > recent_prices[0]:
                    trend = "Bullish"
                elif recent_prices[-1] < recent_prices[0]:
                    trend = "Bearish"
                else:
                    trend = "Sideways"
            else:
                trend = "Unknown"

            # Basic pattern detection (simplified)
            pattern = "Price movement detected"
            if len(prices) >= 5:
                # Check for ascending/descending pattern
                ascending = all(prices[i] <= prices[i+1] for i in range(len(prices)-1))
                descending = all(prices[i] >= prices[i+1] for i in range(len(prices)-1))

                if ascending:
                    pattern = "Ascending pattern"
                elif descending:
                    pattern = "Descending pattern"
                else:
                    pattern = "Consolidation pattern"

            return {
                "trend": trend,
                "pattern": pattern,
                "price_range": {
                    "high": max(prices) if prices else 0,
                    "low": min(prices) if prices else 0
                }
            }

        except Exception as e:
            logger.error(f"Error identifying basic patterns: {str(e)}")
            return {"trend": "Unknown", "pattern": "Analysis error"}

    def _generate_readable_summary(self, ocr_data, basic_patterns):
        """
        Generate human-readable summary text

        Args:
            ocr_data: OCR extracted data
            basic_patterns: Basic pattern analysis

        Returns:
            str: Human-readable summary
        """
        try:
            symbol = ocr_data.get("symbol", "UNKNOWN")
            timeframe = ocr_data.get("timeframe", "UNKNOWN")
            current_price = ocr_data.get("current_price", 0.0)
            trend = basic_patterns.get("trend", "Unknown")
            pattern = basic_patterns.get("pattern", "Unknown")

            price_range = basic_patterns.get("price_range", {})
            high = price_range.get("high", 0)
            low = price_range.get("low", 0)

            summary_parts = []

            # Basic info
            if symbol != "UNKNOWN":
                summary_parts.append(f"Symbol: {symbol}")

            if timeframe != "UNKNOWN":
                summary_parts.append(f"Timeframe: {timeframe}")

            if current_price > 0:
                summary_parts.append(f"Current Price: {current_price:,.5f}")

            # Pattern info
            if trend != "Unknown":
                summary_parts.append(f"Trend: {trend}")

            if pattern != "Unknown":
                summary_parts.append(f"Pattern: {pattern}")

            # Price range
            if high > 0 and low > 0:
                summary_parts.append(f"Price Range: {low:,.5f} - {high:,.5f}")

            if not summary_parts:
                return "Saya dapat melihat chart trading, namun sulit mengidentifikasi detail spesifik. Mohon berikan koreksi jika ada informasi yang tidak sesuai."

            summary = "Berikut yang saya baca dari gambar chart Anda:\n\n"
            summary += "\n".join([f"• {part}" for part in summary_parts])
            summary += "\n\nApakah informasi ini sudah sesuai dengan gambar Anda?"

            return summary

        except Exception as e:
            logger.error(f"Error generating readable summary: {str(e)}")
            return "Terjadi kesalahan saat membaca gambar. Mohon berikan koreksi manual."

def main():
    """Main entry point"""
    try:
        # Check command line arguments
        if len(sys.argv) < 4:
            print(json.dumps({
                "success": False,
                "error": "Usage: python main.py <image_path> <upload_id> <mode> [user_prompt]",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)

        image_path = sys.argv[1]
        upload_id = sys.argv[2]
        mode = sys.argv[3]  # 'summary' or 'full'
        user_prompt = sys.argv[4] if len(sys.argv) > 4 else None

        # Validate mode
        if mode not in ['summary', 'full']:
            print(json.dumps({
                "success": False,
                "error": "Mode must be 'summary' or 'full'",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        # Validate image path
        if not os.path.exists(image_path):
            print(json.dumps({
                "success": False,
                "error": f"Image file not found: {image_path}",
                "timestamp": datetime.now().isoformat()
            }))
            sys.exit(1)
        
        # Initialize engine
        engine = MarketAnalysisEngine()

        # Run analysis based on mode
        if mode == 'summary':
            results = engine.generate_image_summary(image_path, upload_id)
        else:  # mode == 'full'
            results = engine.analyze_chart(image_path, upload_id, user_prompt)
        
        # Output results as JSON
        print(json.dumps(results, indent=2, cls=NumpyEncoder))
        
        # Exit with appropriate code
        sys.exit(0 if results["success"] else 1)
        
    except Exception as e:
        # Handle any unexpected errors
        error_result = {
            "success": False,
            "error": f"Unexpected error: {str(e)}",
            "timestamp": datetime.now().isoformat(),
            "traceback": traceback.format_exc()
        }
        
        print(json.dumps(error_result, indent=2, cls=NumpyEncoder))
        sys.exit(1)

if __name__ == "__main__":
    main()
