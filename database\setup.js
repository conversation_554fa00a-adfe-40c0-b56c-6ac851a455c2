const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../server/.env') });

async function setupDatabase() {
    let connection;
    
    try {
        console.log('🔄 Starting database setup...');
        
        // Connect to MySQL without specifying database
        connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            multipleStatements: true
        });
        
        console.log('✅ Connected to MySQL server');
        
        // Read and execute schema file
        const schemaPath = path.join(__dirname, 'schema.sql');
        const schema = await fs.readFile(schemaPath, 'utf8');
        
        console.log('🔄 Executing database schema...');
        await connection.execute(schema);
        
        console.log('✅ Database schema created successfully');

        // Run migration for new workflow
        console.log('🔄 Running migration for new workflow...');

        // Add new status values to uploads table
        await connection.execute(`
            ALTER TABLE uploads MODIFY COLUMN status
            ENUM('uploaded', 'reading', 'read', 'processing', 'completed', 'failed')
            DEFAULT 'uploaded'
        `);
        console.log('✅ Updated uploads table status enum');

        // Create initial_readings table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS initial_readings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                upload_id INT NOT NULL,
                initial_summary JSON DEFAULT NULL,
                status ENUM('processing', 'completed', 'failed') DEFAULT 'processing',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (upload_id) REFERENCES uploads(id) ON DELETE CASCADE,
                INDEX idx_upload_id (upload_id),
                INDEX idx_status (status)
            )
        `);
        console.log('✅ Created initial_readings table');

        // Add user_prompt field to analyses table
        try {
            await connection.execute(`
                ALTER TABLE analyses ADD COLUMN user_prompt TEXT DEFAULT NULL
            `);
            console.log('✅ Added user_prompt field to analyses table');
        } catch (error) {
            if (error.code === 'ER_DUP_FIELDNAME') {
                console.log('✅ user_prompt field already exists');
            } else {
                throw error;
            }
        }

        console.log('✅ Migration completed successfully');
        
        // Test the connection to the new database
        await connection.changeUser({
            database: process.env.DB_NAME || 'market_analysis'
        });
        
        // Verify tables were created
        const [tables] = await connection.execute('SHOW TABLES');
        console.log('📋 Created tables:');
        tables.forEach(table => {
            console.log(`   - ${Object.values(table)[0]}`);
        });
        
        // Insert some sample data for testing (optional)
        console.log('🔄 Inserting sample data...');
        await insertSampleData(connection);
        
        console.log('🎉 Database setup completed successfully!');
        
    } catch (error) {
        console.error('❌ Database setup failed:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
}

async function insertSampleData(connection) {
    try {
        // Insert sample upload record
        const sampleUpload = {
            filename: 'sample_chart.png',
            original_filename: 'XAUUSD_H1_chart.png',
            filepath: '/uploads/sample_chart.png',
            file_size: 1024000,
            mime_type: 'image/png',
            status: 'completed'
        };
        
        const [uploadResult] = await connection.execute(
            'INSERT INTO uploads (filename, original_filename, filepath, file_size, mime_type, status) VALUES (?, ?, ?, ?, ?, ?)',
            [sampleUpload.filename, sampleUpload.original_filename, sampleUpload.filepath, 
             sampleUpload.file_size, sampleUpload.mime_type, sampleUpload.status]
        );
        
        const uploadId = uploadResult.insertId;
        
        // Insert sample OCR data
        const sampleOCR = {
            upload_id: uploadId,
            extracted_text: 'XAUUSD H1 Chart - Price: 2045.50',
            price_data: JSON.stringify({
                current_price: 2045.50,
                high: 2048.20,
                low: 2042.10,
                open: 2043.80
            }),
            timeframe: 'H1',
            symbol: 'XAUUSD',
            chart_type: 'candlestick',
            ocr_confidence: 85.5,
            processing_time_ms: 1250
        };
        
        await connection.execute(
            'INSERT INTO ocr_data (upload_id, extracted_text, price_data, timeframe, symbol, chart_type, ocr_confidence, processing_time_ms) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [sampleOCR.upload_id, sampleOCR.extracted_text, sampleOCR.price_data, 
             sampleOCR.timeframe, sampleOCR.symbol, sampleOCR.chart_type, 
             sampleOCR.ocr_confidence, sampleOCR.processing_time_ms]
        );
        
        // Insert sample analysis
        const sampleAnalysis = {
            upload_id: uploadId,
            recommendation: 'BUY',
            confidence_score: 78.5,
            stop_loss: 2040.00,
            take_profit: 2055.00,
            entry_price: 2045.50,
            risk_reward_ratio: 2.5,
            price_action_signals: JSON.stringify({
                bullish_engulfing: true,
                support_level: 2042.00,
                resistance_level: 2050.00
            }),
            rsi_analysis: JSON.stringify({
                rsi_value: 45.2,
                signal: 'neutral',
                oversold: false,
                overbought: false
            }),
            analysis_duration_ms: 3500
        };
        
        await connection.execute(
            'INSERT INTO analyses (upload_id, recommendation, confidence_score, stop_loss, take_profit, entry_price, risk_reward_ratio, price_action_signals, rsi_analysis, analysis_duration_ms) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [sampleAnalysis.upload_id, sampleAnalysis.recommendation, sampleAnalysis.confidence_score,
             sampleAnalysis.stop_loss, sampleAnalysis.take_profit, sampleAnalysis.entry_price,
             sampleAnalysis.risk_reward_ratio, sampleAnalysis.price_action_signals, 
             sampleAnalysis.rsi_analysis, sampleAnalysis.analysis_duration_ms]
        );
        
        console.log('✅ Sample data inserted successfully');
        
    } catch (error) {
        console.log('⚠️  Warning: Could not insert sample data:', error.message);
    }
}

// Run setup if called directly
if (require.main === module) {
    setupDatabase();
}

module.exports = { setupDatabase };
