"""
Enhanced Decision Engine Module
Combines all analysis results to generate final trading recommendations with detailed reasoning
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

class EnhancedDecisionEngine:
    """Enhanced decision engine with advanced signal validation and reasoning"""

    def __init__(self):
        # Enhanced weight configuration with dynamic adjustment
        self.base_weights = {
            'price_action': 0.30,
            'smc': 0.25,
            'technical': 0.25,
            'confluence': 0.15,  # New: Signal confluence factor
            'market_regime': 0.05  # New: Market condition factor
        }

        # Advanced risk management parameters
        self.risk_profiles = {
            'conservative': {'rr_ratio': 1.5, 'max_risk': 0.01, 'confidence_min': 0.75},
            'moderate': {'rr_ratio': 2.0, 'max_risk': 0.02, 'confidence_min': 0.60},
            'aggressive': {'rr_ratio': 3.0, 'max_risk': 0.03, 'confidence_min': 0.50}
        }

        # Enhanced confidence thresholds with reasoning levels
        self.confidence_levels = {
            'very_high': {'threshold': 0.85, 'description': 'Sangat Tinggi - Multiple confluence dengan validasi kuat'},
            'high': {'threshold': 0.70, 'description': 'Tinggi - Confluence baik dengan sinyal yang jelas'},
            'medium': {'threshold': 0.55, 'description': 'Sedang - Beberapa sinyal mendukung dengan validasi terbatas'},
            'low': {'threshold': 0.40, 'description': 'Rendah - Sinyal lemah atau bertentangan'},
            'very_low': {'threshold': 0.0, 'description': 'Sangat Rendah - Tidak ada sinyal yang jelas'}
        }

        # Market regime detection parameters
        self.market_regimes = {
            'trending_bull': {'weight_multiplier': 1.2, 'description': 'Pasar dalam tren naik yang kuat'},
            'trending_bear': {'weight_multiplier': 1.2, 'description': 'Pasar dalam tren turun yang kuat'},
            'ranging': {'weight_multiplier': 0.8, 'description': 'Pasar dalam kondisi sideways/ranging'},
            'volatile': {'weight_multiplier': 0.6, 'description': 'Pasar sangat volatile dengan noise tinggi'},
            'breakout': {'weight_multiplier': 1.3, 'description': 'Pasar dalam fase breakout'}
        }

        # Signal validation criteria
        self.validation_criteria = {
            'min_confluence_signals': 2,
            'max_conflicting_ratio': 0.3,
            'min_signal_strength': 0.4,
            'timeframe_consistency_weight': 0.15
        }
    
    def make_decision(self, price_action: Dict, smc: Dict, technical: Dict, ocr_data: Dict, user_context: str = None) -> Dict[str, Any]:
        """
        Enhanced decision making function with detailed reasoning

        Args:
            price_action (Dict): Price action analysis results
            smc (Dict): SMC analysis results
            technical (Dict): Technical indicators results
            ocr_data (Dict): OCR extracted data
            user_context (str): Optional user corrections/context

        Returns:
            Dict: Comprehensive trading decision with detailed reasoning
        """
        try:
            logger.info("Starting enhanced decision making process...")

            # Step 1: Detect market regime
            market_regime = self._detect_market_regime(price_action, smc, technical)
            logger.info(f"Market regime detected: {market_regime}")

            # Step 1.5: Process user context if provided
            user_bias = None
            if user_context:
                user_bias = self._process_user_context(user_context, ocr_data)
                logger.info(f"User context processed: {user_bias}")

            # Step 2: Collect and validate all signals
            all_signals = self._collect_all_signals(price_action, smc, technical)
            validated_signals = self._validate_signals(all_signals, market_regime)

            # Step 3: Calculate confluence analysis
            confluence_analysis = self._analyze_signal_confluence(validated_signals)

            # Step 4: Calculate enhanced weighted scores
            weighted_scores = self._calculate_enhanced_weighted_scores(
                price_action, smc, technical, ocr_data, market_regime, confluence_analysis, user_bias
            )

            # Step 5: Determine primary recommendation with reasoning
            recommendation_analysis = self._determine_recommendation_with_reasoning(
                weighted_scores, validated_signals, confluence_analysis, market_regime
            )

            # Step 6: Calculate advanced confidence with multiple factors
            confidence_analysis = self._calculate_advanced_confidence(
                weighted_scores, validated_signals, confluence_analysis, ocr_data, market_regime
            )

            # Step 7: Generate advanced risk management
            risk_management = self._calculate_advanced_risk_management(
                recommendation_analysis, price_action, smc, technical, ocr_data, confidence_analysis
            )

            # Step 8: Generate detailed reasoning and explanations
            signal_reasoning = self._generate_signal_reasoning(
                validated_signals, confluence_analysis, market_regime, recommendation_analysis
            )

            # Step 9: Create comprehensive decision with explanations
            decision = {
                'recommendation': recommendation_analysis['recommendation'],
                'confidence': confidence_analysis['confidence'],
                'confidence_level': confidence_analysis['level'],
                'confidence_description': confidence_analysis['description'],

                # Enhanced pricing and risk management
                'entry_price': risk_management['entry_price'],
                'stop_loss': risk_management['stop_loss'],
                'take_profit': risk_management['take_profit'],
                'risk_reward_ratio': risk_management['risk_reward_ratio'],
                'position_size': risk_management['position_size'],
                'max_risk_percent': risk_management['max_risk_percent'],

                # Market analysis
                'market_regime': market_regime,
                'market_context': self._analyze_enhanced_market_context(price_action, smc, technical, market_regime),

                # Signal analysis with reasoning
                'signal_reasoning': signal_reasoning,
                'confluence_analysis': confluence_analysis,
                'validated_signals': validated_signals,
                'signal_strength_breakdown': self._breakdown_signal_strength(validated_signals),

                # Decision factors
                'supporting_factors': recommendation_analysis['supporting_factors'],
                'risk_factors': recommendation_analysis['risk_factors'],
                'key_levels': self._identify_enhanced_key_levels(price_action, smc, technical),

                # Execution guidance
                'execution_plan': self._generate_execution_plan(recommendation_analysis, risk_management, market_regime),
                'timeframe_analysis': self._analyze_timeframe_suitability(ocr_data, validated_signals, market_regime),
                'scenario_analysis': self._generate_enhanced_scenario_analysis(validated_signals, recommendation_analysis, market_regime),

                # Technical details
                'weighted_scores': weighted_scores,
                'validation_results': self._summarize_validation_results(all_signals, validated_signals),
                'analysis_timestamp': datetime.now().isoformat(),
                'analysis_version': '2.0_enhanced'
            }

            logger.info(f"Enhanced decision completed: {recommendation_analysis['recommendation']} "
                       f"with {confidence_analysis['confidence']:.2f} confidence ({confidence_analysis['level']})")
            return decision

        except Exception as e:
            logger.error(f"Enhanced decision making failed: {str(e)}")
            return self._generate_enhanced_default_decision(str(e))

    def _detect_market_regime(self, price_action: Dict, smc: Dict, technical: Dict) -> str:
        """
        Detect current market regime for better signal interpretation

        Returns:
            str: Market regime (trending_bull, trending_bear, ranging, volatile, breakout)
        """
        try:
            # Analyze trend strength and direction
            trend_direction = price_action.get('trend', 'NEUTRAL')
            trend_strength = price_action.get('trend_strength', 0.0)

            # Analyze market structure from SMC
            market_structure = smc.get('market_structure', 'NEUTRAL')
            structure_strength = smc.get('structure_strength', 0.0)

            # Analyze technical momentum
            overall_momentum = technical.get('overall_momentum', 'NEUTRAL')

            # Detect volatility from price action
            volatility_score = self._calculate_volatility_score(price_action, technical)

            # Detect breakout potential
            breakout_potential = price_action.get('breakout_potential', 0.0)

            # Determine regime based on multiple factors
            if breakout_potential > 0.7:
                return 'breakout'
            elif volatility_score > 0.8:
                return 'volatile'
            elif trend_strength > 0.7 and trend_direction == 'BULLISH':
                return 'trending_bull'
            elif trend_strength > 0.7 and trend_direction == 'BEARISH':
                return 'trending_bear'
            else:
                return 'ranging'

        except Exception as e:
            logger.error(f"Market regime detection failed: {str(e)}")
            return 'ranging'  # Default to ranging if detection fails

    def _calculate_volatility_score(self, price_action: Dict, technical: Dict) -> float:
        """Calculate volatility score from available data"""
        try:
            # Use Bollinger Bands width as volatility indicator
            bb_data = technical.get('bollinger_bands', {})
            if bb_data.get('detected'):
                bb_width = bb_data.get('width_ratio', 0.5)
                return min(bb_width * 2, 1.0)  # Normalize to 0-1

            # Fallback: use trend strength variation
            trend_strength = price_action.get('trend_strength', 0.5)
            return abs(trend_strength - 0.5) * 2  # Convert to volatility measure

        except Exception:
            return 0.5  # Default moderate volatility

    def _validate_signals(self, all_signals: List[Dict], market_regime: str) -> List[Dict]:
        """
        Validate signals based on market regime and quality criteria

        Args:
            all_signals: Raw signals from all analyzers
            market_regime: Current market regime

        Returns:
            List of validated signals with quality scores
        """
        validated_signals = []

        try:
            for signal in all_signals:
                # Basic validation criteria
                if not self._meets_basic_criteria(signal):
                    continue

                # Market regime compatibility
                regime_compatibility = self._check_regime_compatibility(signal, market_regime)

                # Signal strength validation
                strength_score = self._validate_signal_strength(signal)

                # Add validation metadata
                validated_signal = signal.copy()
                validated_signal.update({
                    'regime_compatibility': regime_compatibility,
                    'strength_score': strength_score,
                    'validation_score': (regime_compatibility + strength_score) / 2,
                    'is_validated': regime_compatibility > 0.5 and strength_score > 0.4
                })

                if validated_signal['is_validated']:
                    validated_signals.append(validated_signal)

            logger.info(f"Validated {len(validated_signals)} out of {len(all_signals)} signals")
            return validated_signals

        except Exception as e:
            logger.error(f"Signal validation failed: {str(e)}")
            return all_signals  # Return original signals if validation fails

    def _meets_basic_criteria(self, signal: Dict) -> bool:
        """Check if signal meets basic quality criteria"""
        required_fields = ['type', 'strength', 'confidence', 'source']

        # Check required fields
        for field in required_fields:
            if field not in signal:
                return False

        # Check minimum strength and confidence
        if signal.get('strength', 0) < self.validation_criteria['min_signal_strength']:
            return False

        if signal.get('confidence', 0) < 0.3:  # Minimum confidence threshold
            return False

        # Check signal type validity
        if signal.get('type') not in ['BUY', 'SELL', 'NEUTRAL']:
            return False

        return True

    def _check_regime_compatibility(self, signal: Dict, market_regime: str) -> float:
        """Check how compatible a signal is with current market regime"""
        signal_type = signal.get('type', 'NEUTRAL')
        signal_source = signal.get('source', '')

        # Compatibility matrix
        compatibility_matrix = {
            'trending_bull': {'BUY': 1.0, 'SELL': 0.3, 'NEUTRAL': 0.5},
            'trending_bear': {'BUY': 0.3, 'SELL': 1.0, 'NEUTRAL': 0.5},
            'ranging': {'BUY': 0.7, 'SELL': 0.7, 'NEUTRAL': 0.8},
            'volatile': {'BUY': 0.4, 'SELL': 0.4, 'NEUTRAL': 0.9},
            'breakout': {'BUY': 0.9, 'SELL': 0.9, 'NEUTRAL': 0.2}
        }

        base_compatibility = compatibility_matrix.get(market_regime, {}).get(signal_type, 0.5)

        # Adjust based on signal source
        if signal_source in ['smc', 'order_block'] and market_regime in ['trending_bull', 'trending_bear']:
            base_compatibility *= 1.1  # SMC signals work well in trending markets
        elif signal_source in ['technical', 'rsi', 'macd'] and market_regime == 'ranging':
            base_compatibility *= 1.1  # Technical indicators work well in ranging markets

        return min(base_compatibility, 1.0)

    def _validate_signal_strength(self, signal: Dict) -> float:
        """Validate and normalize signal strength"""
        raw_strength = signal.get('strength', 0.0)
        confidence = signal.get('confidence', 0.0)
        source = signal.get('source', '')

        # Source reliability weights
        source_weights = {
            'price_action': 1.0,
            'smc': 0.9,
            'order_block': 0.95,
            'technical': 0.8,
            'candlestick_pattern': 0.85,
            'support_resistance': 0.9
        }

        source_weight = source_weights.get(source, 0.7)

        # Calculate weighted strength
        weighted_strength = (raw_strength * 0.6 + confidence * 0.4) * source_weight

        return min(weighted_strength, 1.0)

    def _analyze_signal_confluence(self, validated_signals: List[Dict]) -> Dict[str, Any]:
        """
        Analyze confluence between different signals

        Args:
            validated_signals: List of validated signals

        Returns:
            Dict containing confluence analysis results
        """
        try:
            if not validated_signals:
                return {
                    'confluence_score': 0.0,
                    'confluence_level': 'none',
                    'confluent_signals': [],
                    'conflicting_signals': [],
                    'signal_consensus': 'NEUTRAL'
                }

            # Group signals by type
            buy_signals = [s for s in validated_signals if s.get('type') == 'BUY']
            sell_signals = [s for s in validated_signals if s.get('type') == 'SELL']
            neutral_signals = [s for s in validated_signals if s.get('type') == 'NEUTRAL']

            # Calculate signal strengths
            buy_strength = sum(s.get('validation_score', 0) for s in buy_signals)
            sell_strength = sum(s.get('validation_score', 0) for s in sell_signals)

            # Determine consensus
            total_signals = len(validated_signals)
            if total_signals == 0:
                consensus = 'NEUTRAL'
            elif buy_strength > sell_strength * 1.5:
                consensus = 'BUY'
            elif sell_strength > buy_strength * 1.5:
                consensus = 'SELL'
            else:
                consensus = 'MIXED'

            # Calculate confluence score
            if consensus in ['BUY', 'SELL']:
                dominant_signals = buy_signals if consensus == 'BUY' else sell_signals
                confluence_score = min(len(dominant_signals) / max(total_signals, 1) *
                                     (buy_strength if consensus == 'BUY' else sell_strength), 1.0)
            else:
                confluence_score = 0.3  # Low confluence for mixed signals

            # Classify confluence level
            if confluence_score >= 0.8:
                confluence_level = 'very_high'
            elif confluence_score >= 0.6:
                confluence_level = 'high'
            elif confluence_score >= 0.4:
                confluence_level = 'medium'
            else:
                confluence_level = 'low'

            # Identify confluent and conflicting signals
            if consensus in ['BUY', 'SELL']:
                confluent_signals = buy_signals if consensus == 'BUY' else sell_signals
                conflicting_signals = sell_signals if consensus == 'BUY' else buy_signals
            else:
                confluent_signals = []
                conflicting_signals = validated_signals

            return {
                'confluence_score': confluence_score,
                'confluence_level': confluence_level,
                'confluent_signals': confluent_signals,
                'conflicting_signals': conflicting_signals,
                'signal_consensus': consensus,
                'buy_strength': buy_strength,
                'sell_strength': sell_strength,
                'total_signals': total_signals,
                'signal_distribution': {
                    'buy_count': len(buy_signals),
                    'sell_count': len(sell_signals),
                    'neutral_count': len(neutral_signals)
                }
            }

        except Exception as e:
            logger.error(f"Confluence analysis failed: {str(e)}")
            return {
                'confluence_score': 0.0,
                'confluence_level': 'none',
                'confluent_signals': [],
                'conflicting_signals': validated_signals,
                'signal_consensus': 'NEUTRAL'
            }

    def _calculate_enhanced_weighted_scores(self, price_action: Dict, smc: Dict, technical: Dict,
                                          ocr_data: Dict, market_regime: str, confluence_analysis: Dict, user_bias: Dict = None) -> Dict[str, float]:
        """
        Calculate enhanced weighted scores with market regime and confluence adjustments
        """
        try:
            # Base scores from each analysis type
            base_scores = {
                'price_action_score': price_action.get('price_action_score', 0.0),
                'smc_score': smc.get('smc_score', 0.0),
                'technical_score': technical.get('technical_score', 0.0),
                'ocr_confidence': ocr_data.get('extraction_confidence', 0.0)
            }

            # Market regime adjustments
            regime_multipliers = self.market_regimes.get(market_regime, {}).get('weight_multiplier', 1.0)

            # Confluence bonus
            confluence_bonus = confluence_analysis.get('confluence_score', 0.0) * 0.2

            # User bias adjustment
            user_bias_adjustment = 0.0
            if user_bias and user_bias.get('confidence', 0) > 0:
                user_weight = user_bias.get('context_weight', 0.2)
                user_confidence = user_bias.get('confidence', 0.0)
                user_direction = user_bias.get('direction', 'NEUTRAL')

                if user_direction == 'BULLISH':
                    user_bias_adjustment = user_weight * user_confidence
                elif user_direction == 'BEARISH':
                    user_bias_adjustment = -user_weight * user_confidence

                logger.info(f"User bias adjustment: {user_bias_adjustment}")

            # Calculate adjusted weights based on market regime
            adjusted_weights = self.base_weights.copy()

            # Adjust weights based on market regime
            if market_regime in ['trending_bull', 'trending_bear']:
                adjusted_weights['price_action'] *= 1.1
                adjusted_weights['smc'] *= 1.2
            elif market_regime == 'ranging':
                adjusted_weights['technical'] *= 1.2
                adjusted_weights['smc'] *= 0.9
            elif market_regime == 'volatile':
                adjusted_weights['technical'] *= 0.8
                adjusted_weights['price_action'] *= 0.9

            # Calculate weighted scores
            weighted_scores = {}
            for key, score in base_scores.items():
                weight_key = key.replace('_score', '').replace('_confidence', '')
                if weight_key == 'ocr':
                    weight_key = 'technical'  # Map OCR to technical weight

                weight = adjusted_weights.get(weight_key, 0.2)
                weighted_scores[key] = score * weight * regime_multipliers

            # Add confluence score
            weighted_scores['confluence_score'] = confluence_analysis.get('confluence_score', 0.0) * adjusted_weights.get('confluence', 0.15)

            # Calculate total weighted score
            total_weighted = sum(weighted_scores.values()) + confluence_bonus + user_bias_adjustment
            weighted_scores['total_weighted_score'] = max(-1.0, min(total_weighted, 1.0))
            weighted_scores['user_bias_adjustment'] = user_bias_adjustment

            # Add regime and confluence metadata
            weighted_scores.update({
                'market_regime': market_regime,
                'regime_multiplier': regime_multipliers,
                'confluence_bonus': confluence_bonus,
                'adjusted_weights': adjusted_weights
            })

            return weighted_scores

        except Exception as e:
            logger.error(f"Enhanced weighted scoring failed: {str(e)}")
            # Fallback to basic scoring
            return {
                'price_action_score': price_action.get('price_action_score', 0.0) * 0.3,
                'smc_score': smc.get('smc_score', 0.0) * 0.25,
                'technical_score': technical.get('technical_score', 0.0) * 0.25,
                'total_weighted_score': 0.5
            }

    def _determine_recommendation_with_reasoning(self, weighted_scores: Dict, validated_signals: List[Dict],
                                               confluence_analysis: Dict, market_regime: str) -> Dict[str, Any]:
        """
        Determine trading recommendation with detailed reasoning

        Returns:
            Dict containing recommendation and supporting reasoning
        """
        try:
            total_score = weighted_scores.get('total_weighted_score', 0.0)
            consensus = confluence_analysis.get('signal_consensus', 'NEUTRAL')
            confluence_score = confluence_analysis.get('confluence_score', 0.0)

            # Decision thresholds based on market regime
            regime_thresholds = {
                'trending_bull': {'buy': 0.55, 'sell': 0.70},
                'trending_bear': {'buy': 0.70, 'sell': 0.55},
                'ranging': {'buy': 0.65, 'sell': 0.65},
                'volatile': {'buy': 0.75, 'sell': 0.75},
                'breakout': {'buy': 0.60, 'sell': 0.60}
            }

            thresholds = regime_thresholds.get(market_regime, {'buy': 0.65, 'sell': 0.65})

            # Determine recommendation
            recommendation = 'HOLD'  # Default
            primary_reasons = []
            supporting_factors = []
            risk_factors = []

            if consensus == 'BUY' and total_score >= thresholds['buy'] and confluence_score >= 0.4:
                recommendation = 'BUY'
                primary_reasons.append(f"Sinyal BUY dominan dengan confluence score {confluence_score:.2f}")
                primary_reasons.append(f"Total weighted score {total_score:.2f} melampaui threshold {thresholds['buy']}")

                # Add specific supporting factors
                buy_signals = confluence_analysis.get('confluent_signals', [])
                for signal in buy_signals[:3]:  # Top 3 signals
                    supporting_factors.append(f"{signal.get('source', 'Unknown')}: {signal.get('type', '')} "
                                            f"(strength: {signal.get('strength', 0):.2f})")

            elif consensus == 'SELL' and total_score >= thresholds['sell'] and confluence_score >= 0.4:
                recommendation = 'SELL'
                primary_reasons.append(f"Sinyal SELL dominan dengan confluence score {confluence_score:.2f}")
                primary_reasons.append(f"Total weighted score {total_score:.2f} melampaui threshold {thresholds['sell']}")

                # Add specific supporting factors
                sell_signals = confluence_analysis.get('confluent_signals', [])
                for signal in sell_signals[:3]:  # Top 3 signals
                    supporting_factors.append(f"{signal.get('source', 'Unknown')}: {signal.get('type', '')} "
                                            f"(strength: {signal.get('strength', 0):.2f})")
            else:
                recommendation = 'HOLD'
                if total_score < 0.5:
                    primary_reasons.append(f"Total weighted score {total_score:.2f} terlalu rendah untuk trading")
                if confluence_score < 0.4:
                    primary_reasons.append(f"Confluence score {confluence_score:.2f} menunjukkan sinyal lemah")
                if consensus == 'MIXED':
                    primary_reasons.append("Sinyal bertentangan antara BUY dan SELL")

            # Add market regime context
            regime_description = self.market_regimes.get(market_regime, {}).get('description', 'Unknown market condition')
            primary_reasons.append(f"Market regime: {regime_description}")

            # Identify risk factors
            conflicting_signals = confluence_analysis.get('conflicting_signals', [])
            if conflicting_signals:
                risk_factors.append(f"{len(conflicting_signals)} sinyal bertentangan dengan rekomendasi utama")

            if market_regime == 'volatile':
                risk_factors.append("Pasar volatile - risiko whipsaw tinggi")

            if confluence_score < 0.6:
                risk_factors.append("Confluence rendah - validasi sinyal terbatas")

            return {
                'recommendation': recommendation,
                'primary_reasons': primary_reasons,
                'supporting_factors': supporting_factors,
                'risk_factors': risk_factors,
                'decision_threshold': thresholds,
                'market_regime_impact': regime_description
            }

        except Exception as e:
            logger.error(f"Recommendation determination failed: {str(e)}")
            return {
                'recommendation': 'HOLD',
                'primary_reasons': ['Error dalam analisis - defaulting ke HOLD'],
                'supporting_factors': [],
                'risk_factors': ['System error occurred'],
                'decision_threshold': {},
                'market_regime_impact': 'Unknown'
            }

    def _calculate_advanced_confidence(self, weighted_scores: Dict, validated_signals: List[Dict],
                                     confluence_analysis: Dict, ocr_data: Dict, market_regime: str) -> Dict[str, Any]:
        """
        Calculate advanced confidence with multiple factors and detailed explanation

        Returns:
            Dict containing confidence score, level, and explanation
        """
        try:
            # Base confidence from weighted scores
            base_confidence = weighted_scores.get('total_weighted_score', 0.0)

            # Confluence factor (30% weight)
            confluence_factor = confluence_analysis.get('confluence_score', 0.0) * 0.3

            # Signal quality factor (25% weight)
            if validated_signals:
                avg_validation_score = sum(s.get('validation_score', 0) for s in validated_signals) / len(validated_signals)
                signal_quality_factor = avg_validation_score * 0.25
            else:
                signal_quality_factor = 0.0

            # OCR quality factor (10% weight)
            ocr_factor = ocr_data.get('extraction_confidence', 0.0) * 0.1

            # Market regime stability factor (15% weight)
            regime_stability = self._calculate_regime_stability(market_regime, validated_signals)
            regime_factor = regime_stability * 0.15

            # Signal consistency factor (20% weight)
            consistency_factor = self._calculate_signal_consistency(validated_signals) * 0.2

            # Calculate total confidence
            total_confidence = (
                base_confidence * 0.4 +  # Reduced weight for base
                confluence_factor +
                signal_quality_factor +
                ocr_factor +
                regime_factor +
                consistency_factor
            )

            # Normalize to 0-1 range
            total_confidence = min(max(total_confidence, 0.0), 1.0)

            # Determine confidence level and description
            confidence_level = 'very_low'
            description = 'Sangat Rendah - Tidak ada sinyal yang jelas'

            for level, config in self.confidence_levels.items():
                if total_confidence >= config['threshold']:
                    confidence_level = level
                    description = config['description']
                    break

            # Generate detailed confidence breakdown
            confidence_breakdown = {
                'base_score': base_confidence,
                'confluence_contribution': confluence_factor,
                'signal_quality_contribution': signal_quality_factor,
                'ocr_quality_contribution': ocr_factor,
                'regime_stability_contribution': regime_factor,
                'consistency_contribution': consistency_factor
            }

            return {
                'confidence': total_confidence,
                'level': confidence_level,
                'description': description,
                'breakdown': confidence_breakdown,
                'factors_explanation': self._explain_confidence_factors(confidence_breakdown, market_regime)
            }

        except Exception as e:
            logger.error(f"Advanced confidence calculation failed: {str(e)}")
            return {
                'confidence': 0.3,
                'level': 'low',
                'description': 'Rendah - Error dalam perhitungan confidence',
                'breakdown': {},
                'factors_explanation': []
            }

    def _calculate_regime_stability(self, market_regime: str, validated_signals: List[Dict]) -> float:
        """Calculate how stable the current market regime is"""
        regime_stability_scores = {
            'trending_bull': 0.8,
            'trending_bear': 0.8,
            'ranging': 0.6,
            'volatile': 0.3,
            'breakout': 0.5
        }

        base_stability = regime_stability_scores.get(market_regime, 0.5)

        # Adjust based on signal consistency with regime
        if validated_signals:
            regime_compatible_signals = sum(1 for s in validated_signals
                                          if s.get('regime_compatibility', 0) > 0.7)
            compatibility_ratio = regime_compatible_signals / len(validated_signals)
            base_stability *= (0.5 + compatibility_ratio * 0.5)

        return base_stability

    def _calculate_signal_consistency(self, validated_signals: List[Dict]) -> float:
        """Calculate consistency between different signals"""
        if not validated_signals:
            return 0.0

        # Group signals by type
        signal_types = [s.get('type') for s in validated_signals if s.get('type') in ['BUY', 'SELL']]

        if not signal_types:
            return 0.0

        # Calculate consistency as ratio of most common signal type
        from collections import Counter
        type_counts = Counter(signal_types)
        most_common_count = type_counts.most_common(1)[0][1]
        consistency = most_common_count / len(signal_types)

        return consistency

    def _explain_confidence_factors(self, breakdown: Dict, market_regime: str) -> List[str]:
        """Generate human-readable explanations for confidence factors"""
        explanations = []

        if breakdown.get('confluence_contribution', 0) > 0.15:
            explanations.append("Confluence tinggi antar sinyal meningkatkan kepercayaan")
        elif breakdown.get('confluence_contribution', 0) < 0.05:
            explanations.append("Confluence rendah mengurangi kepercayaan")

        if breakdown.get('signal_quality_contribution', 0) > 0.15:
            explanations.append("Kualitas sinyal yang baik mendukung analisis")

        if breakdown.get('regime_stability_contribution', 0) > 0.1:
            explanations.append(f"Kondisi pasar {market_regime} mendukung analisis")

        if breakdown.get('consistency_contribution', 0) > 0.15:
            explanations.append("Konsistensi sinyal yang baik meningkatkan kepercayaan")

        return explanations

    def _generate_signal_reasoning(self, validated_signals: List[Dict], confluence_analysis: Dict,
                                 market_regime: str, recommendation_analysis: Dict) -> Dict[str, Any]:
        """
        Generate detailed reasoning for why signals appeared and their significance

        Returns:
            Dict containing detailed signal reasoning and explanations
        """
        try:
            reasoning = {
                'signal_explanations': [],
                'confluence_reasoning': '',
                'market_context_impact': '',
                'strength_analysis': {},
                'risk_assessment': []
            }

            # Explain individual signals
            for signal in validated_signals[:5]:  # Top 5 signals
                explanation = self._explain_individual_signal(signal, market_regime)
                reasoning['signal_explanations'].append(explanation)

            # Explain confluence
            confluence_score = confluence_analysis.get('confluence_score', 0.0)
            consensus = confluence_analysis.get('signal_consensus', 'NEUTRAL')

            if confluence_score >= 0.7:
                reasoning['confluence_reasoning'] = (
                    f"Confluence sangat kuat ({confluence_score:.2f}) dengan {len(confluence_analysis.get('confluent_signals', []))} "
                    f"sinyal mendukung arah {consensus}. Ini menunjukkan probabilitas tinggi untuk pergerakan harga sesuai sinyal."
                )
            elif confluence_score >= 0.4:
                reasoning['confluence_reasoning'] = (
                    f"Confluence moderat ({confluence_score:.2f}) dengan beberapa sinyal mendukung {consensus}. "
                    f"Perlu konfirmasi tambahan sebelum eksekusi."
                )
            else:
                reasoning['confluence_reasoning'] = (
                    f"Confluence lemah ({confluence_score:.2f}) dengan sinyal yang bertentangan. "
                    f"Tidak disarankan untuk trading dalam kondisi ini."
                )

            # Explain market context impact
            regime_description = self.market_regimes.get(market_regime, {}).get('description', 'Unknown')
            reasoning['market_context_impact'] = (
                f"Kondisi pasar saat ini ({market_regime}): {regime_description}. "
                f"Hal ini {'mendukung' if confluence_score > 0.5 else 'tidak mendukung'} sinyal yang terdeteksi."
            )

            # Analyze signal strength distribution
            if validated_signals:
                strengths = [s.get('validation_score', 0) for s in validated_signals]
                reasoning['strength_analysis'] = {
                    'average_strength': sum(strengths) / len(strengths),
                    'strongest_signal': max(strengths),
                    'weakest_signal': min(strengths),
                    'strength_consistency': 1.0 - (max(strengths) - min(strengths)) if strengths else 0.0
                }

            # Risk assessment
            risk_factors = recommendation_analysis.get('risk_factors', [])
            for risk in risk_factors:
                reasoning['risk_assessment'].append({
                    'factor': risk,
                    'impact': 'medium',  # Could be enhanced with specific impact analysis
                    'mitigation': self._suggest_risk_mitigation(risk)
                })

            return reasoning

        except Exception as e:
            logger.error(f"Signal reasoning generation failed: {str(e)}")
            return {
                'signal_explanations': [],
                'confluence_reasoning': 'Error dalam analisis confluence',
                'market_context_impact': 'Error dalam analisis konteks pasar',
                'strength_analysis': {},
                'risk_assessment': []
            }

    def _explain_individual_signal(self, signal: Dict, market_regime: str) -> Dict[str, str]:
        """Explain why an individual signal appeared and its significance"""
        source = signal.get('source', 'unknown')
        signal_type = signal.get('type', 'NEUTRAL')
        strength = signal.get('strength', 0.0)
        confidence = signal.get('confidence', 0.0)

        # Base explanations by source
        source_explanations = {
            'price_action': f"Pola price action menunjukkan sinyal {signal_type} dengan kekuatan {strength:.2f}",
            'smc': f"Analisis Smart Money Concept mengidentifikasi {signal_type} signal dari institutional behavior",
            'order_block': f"Order block terdeteksi yang mendukung pergerakan {signal_type}",
            'technical': f"Indikator teknikal memberikan sinyal {signal_type} dengan confidence {confidence:.2f}",
            'candlestick_pattern': f"Pola candlestick menunjukkan kemungkinan {signal_type} berdasarkan formasi harga",
            'support_resistance': f"Level support/resistance mengindikasikan potensi {signal_type}"
        }

        base_explanation = source_explanations.get(source, f"Sinyal {signal_type} dari {source}")

        # Add market regime context
        regime_context = ""
        regime_compatibility = signal.get('regime_compatibility', 0.5)
        if regime_compatibility > 0.7:
            regime_context = f" Sinyal ini sangat sesuai dengan kondisi pasar {market_regime} saat ini."
        elif regime_compatibility < 0.4:
            regime_context = f" Perlu hati-hati karena sinyal ini kurang sesuai dengan kondisi pasar {market_regime}."

        # Add strength assessment
        strength_assessment = ""
        if strength > 0.8:
            strength_assessment = " Sinyal ini memiliki kekuatan yang sangat tinggi."
        elif strength > 0.6:
            strength_assessment = " Sinyal ini memiliki kekuatan yang baik."
        elif strength < 0.4:
            strength_assessment = " Sinyal ini relatif lemah dan perlu konfirmasi tambahan."

        return {
            'source': source,
            'type': signal_type,
            'explanation': base_explanation + regime_context + strength_assessment,
            'strength': strength,
            'confidence': confidence,
            'regime_compatibility': regime_compatibility
        }

    def _suggest_risk_mitigation(self, risk_factor: str) -> str:
        """Suggest mitigation strategies for identified risk factors"""
        mitigation_strategies = {
            'volatile': 'Gunakan position size yang lebih kecil dan stop loss yang lebih ketat',
            'conflicting': 'Tunggu konfirmasi tambahan atau hindari trading',
            'low_confluence': 'Cari konfirmasi dari timeframe yang berbeda',
            'weak_signal': 'Pertimbangkan untuk menunggu setup yang lebih kuat',
            'regime_mismatch': 'Sesuaikan strategi dengan kondisi pasar saat ini'
        }

        for key, strategy in mitigation_strategies.items():
            if key in risk_factor.lower():
                return strategy

        return 'Monitor kondisi pasar dan gunakan risk management yang ketat'

    def _calculate_advanced_risk_management(self, recommendation_analysis: Dict, price_action: Dict,
                                          smc: Dict, technical: Dict, ocr_data: Dict, confidence_analysis: Dict) -> Dict[str, Any]:
        """
        Calculate advanced risk management with dynamic stop loss and take profit

        Returns:
            Dict containing comprehensive risk management parameters
        """
        try:
            recommendation = recommendation_analysis.get('recommendation', 'HOLD')
            confidence = confidence_analysis.get('confidence', 0.5)
            current_price = ocr_data.get('current_price', 0.0)

            if recommendation == 'HOLD' or current_price == 0.0:
                return self._generate_default_risk_management()

            # Determine risk profile based on confidence
            if confidence >= 0.8:
                risk_profile = 'aggressive'
            elif confidence >= 0.6:
                risk_profile = 'moderate'
            else:
                risk_profile = 'conservative'

            profile_config = self.risk_profiles.get(risk_profile, self.risk_profiles['moderate'])

            # Calculate dynamic stop loss based on multiple factors
            stop_loss = self._calculate_dynamic_stop_loss(
                recommendation, current_price, price_action, smc, technical, confidence
            )

            # Calculate dynamic take profit
            take_profit = self._calculate_dynamic_take_profit(
                recommendation, current_price, stop_loss, profile_config, price_action, smc
            )

            # Calculate risk-reward ratio
            if recommendation == 'BUY':
                risk = abs(current_price - stop_loss)
                reward = abs(take_profit - current_price)
            else:  # SELL
                risk = abs(stop_loss - current_price)
                reward = abs(current_price - take_profit)

            risk_reward_ratio = reward / risk if risk > 0 else 0.0

            # Calculate position size based on risk
            position_size = self._calculate_dynamic_position_size(
                risk, profile_config['max_risk'], confidence
            )

            return {
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'risk_reward_ratio': risk_reward_ratio,
                'position_size': position_size,
                'max_risk_percent': profile_config['max_risk'],
                'risk_profile': risk_profile,
                'risk_amount': risk,
                'reward_amount': reward,
                'risk_management_notes': self._generate_risk_notes(risk_profile, confidence, risk_reward_ratio)
            }

        except Exception as e:
            logger.error(f"Advanced risk management calculation failed: {str(e)}")
            return self._generate_default_risk_management()

    def _calculate_dynamic_stop_loss(self, recommendation: str, current_price: float,
                                   price_action: Dict, smc: Dict, technical: Dict, confidence: float) -> float:
        """Calculate dynamic stop loss based on market structure and confidence"""
        if current_price == 0.0:
            return current_price

        # Base stop loss percentage based on confidence
        base_stop_pct = 0.02 if confidence > 0.7 else 0.03 if confidence > 0.5 else 0.04

        # Adjust based on market structure
        support_resistance = technical.get('support_resistance', {})
        key_levels = support_resistance.get('levels', [])

        if recommendation == 'BUY':
            # Find nearest support level
            support_levels = [level for level in key_levels if level < current_price]
            if support_levels:
                nearest_support = max(support_levels)
                structure_stop = nearest_support * 0.995  # Slightly below support
                base_stop = current_price * (1 - base_stop_pct)
                return max(structure_stop, base_stop)  # Use the higher (less risky) stop
            else:
                return current_price * (1 - base_stop_pct)
        else:  # SELL
            # Find nearest resistance level
            resistance_levels = [level for level in key_levels if level > current_price]
            if resistance_levels:
                nearest_resistance = min(resistance_levels)
                structure_stop = nearest_resistance * 1.005  # Slightly above resistance
                base_stop = current_price * (1 + base_stop_pct)
                return min(structure_stop, base_stop)  # Use the lower (less risky) stop
            else:
                return current_price * (1 + base_stop_pct)

    def _calculate_dynamic_take_profit(self, recommendation: str, current_price: float,
                                     stop_loss: float, profile_config: Dict, price_action: Dict, smc: Dict) -> float:
        """Calculate dynamic take profit based on risk-reward ratio and market structure"""
        if current_price == 0.0 or stop_loss == 0.0:
            return current_price

        # Calculate risk distance
        risk_distance = abs(current_price - stop_loss)

        # Base take profit using risk-reward ratio
        target_rr = profile_config.get('rr_ratio', 2.0)

        if recommendation == 'BUY':
            base_take_profit = current_price + (risk_distance * target_rr)
        else:  # SELL
            base_take_profit = current_price - (risk_distance * target_rr)

        # Adjust based on market structure (resistance/support levels)
        # This could be enhanced with more sophisticated level detection

        return base_take_profit

    def _calculate_dynamic_position_size(self, risk_amount: float, max_risk_pct: float, confidence: float) -> float:
        """Calculate position size based on risk and confidence"""
        # Base position size as percentage of account
        base_position_pct = 0.02  # 2% base position

        # Adjust based on confidence
        confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 to 1.0 range

        # Adjust based on risk-reward
        adjusted_position_pct = base_position_pct * confidence_multiplier

        # Ensure we don't exceed maximum risk
        max_position_pct = max_risk_pct / (risk_amount / 100) if risk_amount > 0 else base_position_pct

        return min(adjusted_position_pct, max_position_pct, 0.05)  # Cap at 5%

    def _generate_risk_notes(self, risk_profile: str, confidence: float, risk_reward_ratio: float) -> List[str]:
        """Generate risk management notes and recommendations"""
        notes = []

        notes.append(f"Risk profile: {risk_profile.upper()} berdasarkan confidence {confidence:.2f}")

        if risk_reward_ratio >= 2.0:
            notes.append(f"Risk-reward ratio {risk_reward_ratio:.2f} sangat baik untuk trading")
        elif risk_reward_ratio >= 1.5:
            notes.append(f"Risk-reward ratio {risk_reward_ratio:.2f} acceptable untuk trading")
        else:
            notes.append(f"Risk-reward ratio {risk_reward_ratio:.2f} kurang optimal - pertimbangkan untuk skip")

        if confidence < 0.6:
            notes.append("Confidence rendah - gunakan position size yang lebih kecil")

        notes.append("Selalu gunakan stop loss dan jangan move stop loss melawan posisi")

        return notes

    def _generate_default_risk_management(self) -> Dict[str, Any]:
        """Generate default risk management for error cases"""
        return {
            'entry_price': 0.0,
            'stop_loss': 0.0,
            'take_profit': 0.0,
            'risk_reward_ratio': 0.0,
            'position_size': 0.01,
            'max_risk_percent': 0.02,
            'risk_profile': 'conservative',
            'risk_amount': 0.0,
            'reward_amount': 0.0,
            'risk_management_notes': ['Error dalam perhitungan risk management - gunakan manual calculation']
        }

    def _analyze_enhanced_market_context(self, price_action: Dict, smc: Dict, technical: Dict, market_regime: str) -> Dict[str, Any]:
        """Analyze enhanced market context with regime-specific insights"""
        try:
            context = {
                'regime': market_regime,
                'regime_description': self.market_regimes.get(market_regime, {}).get('description', 'Unknown'),
                'trend_analysis': {
                    'direction': price_action.get('trend', 'NEUTRAL'),
                    'strength': price_action.get('trend_strength', 0.0),
                    'consistency': self._assess_trend_consistency(price_action, technical)
                },
                'structure_analysis': {
                    'smc_structure': smc.get('market_structure', 'NEUTRAL'),
                    'structure_strength': smc.get('structure_strength', 0.0),
                    'institutional_bias': smc.get('institutional_bias', 'NEUTRAL')
                },
                'momentum_analysis': {
                    'overall_momentum': technical.get('overall_momentum', 'NEUTRAL'),
                    'trend_confirmation': technical.get('trend_confirmation', 'NEUTRAL'),
                    'momentum_strength': self._calculate_momentum_strength(technical)
                },
                'market_conditions': self._assess_market_conditions(market_regime, price_action, technical)
            }

            return context

        except Exception as e:
            logger.error(f"Enhanced market context analysis failed: {str(e)}")
            return {
                'regime': 'unknown',
                'regime_description': 'Error in analysis',
                'trend_analysis': {},
                'structure_analysis': {},
                'momentum_analysis': {},
                'market_conditions': []
            }

    def _assess_trend_consistency(self, price_action: Dict, technical: Dict) -> float:
        """Assess consistency between price action and technical trend"""
        pa_trend = price_action.get('trend', 'NEUTRAL')
        tech_trend = technical.get('trend_confirmation', 'NEUTRAL')

        if pa_trend == tech_trend and pa_trend != 'NEUTRAL':
            return 0.9
        elif pa_trend != 'NEUTRAL' and tech_trend != 'NEUTRAL':
            return 0.3  # Conflicting trends
        else:
            return 0.5  # Neutral/unclear

    def _calculate_momentum_strength(self, technical: Dict) -> float:
        """Calculate overall momentum strength from technical indicators"""
        rsi_data = technical.get('rsi', {})
        macd_data = technical.get('macd', {})

        momentum_score = 0.0
        factors = 0

        if rsi_data.get('detected'):
            rsi_strength = rsi_data.get('strength', 0.0)
            momentum_score += rsi_strength
            factors += 1

        if macd_data.get('detected'):
            macd_strength = macd_data.get('strength', 0.0)
            momentum_score += macd_strength
            factors += 1

        return momentum_score / factors if factors > 0 else 0.5

    def _assess_market_conditions(self, market_regime: str, price_action: Dict, technical: Dict) -> List[str]:
        """Assess current market conditions and provide insights"""
        conditions = []

        # Regime-specific conditions
        if market_regime == 'trending_bull':
            conditions.append("Pasar dalam tren naik yang kuat - cocok untuk buy on dips strategy")
        elif market_regime == 'trending_bear':
            conditions.append("Pasar dalam tren turun yang kuat - cocok untuk sell on rallies strategy")
        elif market_regime == 'ranging':
            conditions.append("Pasar sideways - cocok untuk range trading strategy")
        elif market_regime == 'volatile':
            conditions.append("Pasar sangat volatile - gunakan position size kecil dan stop loss ketat")
        elif market_regime == 'breakout':
            conditions.append("Pasar dalam fase breakout - peluang untuk momentum trading")

        # Technical conditions
        overall_momentum = technical.get('overall_momentum', 'NEUTRAL')
        if overall_momentum == 'STRONG_BULLISH':
            conditions.append("Momentum bullish yang kuat mendukung posisi long")
        elif overall_momentum == 'STRONG_BEARISH':
            conditions.append("Momentum bearish yang kuat mendukung posisi short")

        return conditions

    def _generate_execution_plan(self, recommendation_analysis: Dict, risk_management: Dict, market_regime: str) -> Dict[str, Any]:
        """Generate detailed execution plan for the trading recommendation"""
        try:
            recommendation = recommendation_analysis.get('recommendation', 'HOLD')

            if recommendation == 'HOLD':
                return {
                    'action': 'HOLD',
                    'execution_steps': ['Tunggu setup yang lebih baik', 'Monitor kondisi pasar'],
                    'timing_guidance': 'Tidak ada timing khusus - tunggu sinyal yang lebih kuat',
                    'entry_strategy': 'Tidak ada entry saat ini',
                    'exit_strategy': 'Tidak applicable',
                    'monitoring_points': ['Perubahan market regime', 'Munculnya sinyal baru']
                }

            # Generate execution steps
            execution_steps = []
            if recommendation == 'BUY':
                execution_steps = [
                    f"Siapkan order BUY di level {risk_management.get('entry_price', 0.0):.5f}",
                    f"Set stop loss di {risk_management.get('stop_loss', 0.0):.5f}",
                    f"Set take profit di {risk_management.get('take_profit', 0.0):.5f}",
                    f"Gunakan position size {risk_management.get('position_size', 0.01)*100:.1f}% dari account"
                ]
            else:  # SELL
                execution_steps = [
                    f"Siapkan order SELL di level {risk_management.get('entry_price', 0.0):.5f}",
                    f"Set stop loss di {risk_management.get('stop_loss', 0.0):.5f}",
                    f"Set take profit di {risk_management.get('take_profit', 0.0):.5f}",
                    f"Gunakan position size {risk_management.get('position_size', 0.01)*100:.1f}% dari account"
                ]

            # Timing guidance based on market regime
            timing_guidance = self._generate_timing_guidance(recommendation, market_regime)

            # Entry and exit strategies
            entry_strategy = self._generate_entry_strategy(recommendation, market_regime)
            exit_strategy = self._generate_exit_strategy(recommendation, risk_management)

            # Monitoring points
            monitoring_points = [
                'Pergerakan harga mendekati stop loss atau take profit',
                'Perubahan kondisi pasar atau market regime',
                'Munculnya sinyal konfirmasi atau kontradiksi',
                'Volume dan momentum pergerakan harga'
            ]

            return {
                'action': recommendation,
                'execution_steps': execution_steps,
                'timing_guidance': timing_guidance,
                'entry_strategy': entry_strategy,
                'exit_strategy': exit_strategy,
                'monitoring_points': monitoring_points
            }

        except Exception as e:
            logger.error(f"Execution plan generation failed: {str(e)}")
            return {
                'action': 'HOLD',
                'execution_steps': ['Error dalam generate execution plan'],
                'timing_guidance': 'Manual analysis required',
                'entry_strategy': 'Manual analysis required',
                'exit_strategy': 'Manual analysis required',
                'monitoring_points': ['Monitor manually']
            }

    def _generate_timing_guidance(self, recommendation: str, market_regime: str) -> str:
        """Generate timing guidance based on recommendation and market regime"""
        timing_map = {
            ('BUY', 'trending_bull'): 'Entry pada pullback ke support atau moving average',
            ('BUY', 'ranging'): 'Entry di area support range dengan konfirmasi reversal',
            ('BUY', 'breakout'): 'Entry pada breakout confirmation dengan volume',
            ('SELL', 'trending_bear'): 'Entry pada rally ke resistance atau moving average',
            ('SELL', 'ranging'): 'Entry di area resistance range dengan konfirmasi reversal',
            ('SELL', 'breakout'): 'Entry pada breakdown confirmation dengan volume'
        }

        return timing_map.get((recommendation, market_regime), 'Entry dengan konfirmasi price action')

    def _generate_entry_strategy(self, recommendation: str, market_regime: str) -> str:
        """Generate entry strategy based on recommendation and market regime"""
        if recommendation == 'BUY':
            if market_regime in ['trending_bull', 'breakout']:
                return 'Market order atau limit order sedikit di atas current price untuk momentum entry'
            else:
                return 'Limit order di level support dengan konfirmasi bounce'
        else:  # SELL
            if market_regime in ['trending_bear', 'breakout']:
                return 'Market order atau limit order sedikit di bawah current price untuk momentum entry'
            else:
                return 'Limit order di level resistance dengan konfirmasi rejection'

    def _generate_exit_strategy(self, recommendation: str, risk_management: Dict) -> str:
        """Generate exit strategy based on risk management parameters"""
        rr_ratio = risk_management.get('risk_reward_ratio', 0.0)

        if rr_ratio >= 2.0:
            return f"Exit di take profit {risk_management.get('take_profit', 0.0):.5f} atau trailing stop setelah 1:1 RR"
        else:
            return f"Exit di take profit {risk_management.get('take_profit', 0.0):.5f} atau manual exit jika momentum melemah"

    def _identify_enhanced_key_levels(self, price_action: Dict, smc: Dict, technical: Dict) -> List[Dict[str, Any]]:
        """Identify enhanced key levels with detailed information"""
        try:
            key_levels = []

            # Price action levels
            pa_levels = price_action.get('key_levels', [])
            for level in pa_levels:
                key_levels.append({
                    'price': level,
                    'type': 'support_resistance',
                    'source': 'price_action',
                    'strength': 0.7,
                    'description': f"Level support/resistance dari price action di {level:.5f}"
                })

            # SMC levels
            smc_zones = smc.get('key_zones', [])
            for zone in smc_zones:
                if isinstance(zone, dict):
                    key_levels.append({
                        'price': zone.get('price', 0.0),
                        'type': zone.get('type', 'order_block'),
                        'source': 'smc',
                        'strength': zone.get('strength', 0.8),
                        'description': f"SMC {zone.get('type', 'zone')} di level {zone.get('price', 0.0):.5f}"
                    })

            # Technical levels
            tech_sr = technical.get('support_resistance', {})
            tech_levels = tech_sr.get('levels', [])
            for level in tech_levels:
                key_levels.append({
                    'price': level,
                    'type': 'technical_sr',
                    'source': 'technical',
                    'strength': 0.6,
                    'description': f"Level teknikal support/resistance di {level:.5f}"
                })

            # Sort by strength and remove duplicates
            key_levels.sort(key=lambda x: x['strength'], reverse=True)

            # Remove levels that are too close to each other (within 0.1% price difference)
            filtered_levels = []
            for level in key_levels:
                is_duplicate = False
                for existing in filtered_levels:
                    if abs(level['price'] - existing['price']) / existing['price'] < 0.001:  # 0.1% threshold
                        is_duplicate = True
                        break
                if not is_duplicate:
                    filtered_levels.append(level)

            return filtered_levels[:10]  # Return top 10 levels

        except Exception as e:
            logger.error(f"Enhanced key levels identification failed: {str(e)}")
            return []

    def _analyze_timeframe_suitability(self, ocr_data: Dict, validated_signals: List[Dict], market_regime: str) -> Dict[str, Any]:
        """Analyze timeframe suitability for the trading setup"""
        try:
            current_timeframe = ocr_data.get('timeframe', 'UNKNOWN')

            # Timeframe suitability matrix
            timeframe_suitability = {
                'M1': {'day_trading': 0.9, 'swing_trading': 0.1, 'scalping': 1.0},
                'M5': {'day_trading': 0.9, 'swing_trading': 0.2, 'scalping': 0.8},
                'M15': {'day_trading': 0.8, 'swing_trading': 0.4, 'scalping': 0.6},
                'M30': {'day_trading': 0.7, 'swing_trading': 0.6, 'scalping': 0.3},
                'H1': {'day_trading': 0.6, 'swing_trading': 0.8, 'scalping': 0.2},
                'H4': {'day_trading': 0.3, 'swing_trading': 0.9, 'scalping': 0.1},
                'D1': {'day_trading': 0.1, 'swing_trading': 0.9, 'scalping': 0.0}
            }

            current_suitability = timeframe_suitability.get(current_timeframe, {
                'day_trading': 0.5, 'swing_trading': 0.5, 'scalping': 0.5
            })

            # Adjust based on market regime
            if market_regime == 'volatile':
                current_suitability['scalping'] *= 0.5  # Reduce scalping in volatile markets
            elif market_regime in ['trending_bull', 'trending_bear']:
                current_suitability['swing_trading'] *= 1.2  # Boost swing trading in trends

            # Determine best trading style
            best_style = max(current_suitability.items(), key=lambda x: x[1])

            return {
                'current_timeframe': current_timeframe,
                'suitability_scores': current_suitability,
                'recommended_style': best_style[0],
                'style_confidence': best_style[1],
                'timeframe_notes': self._generate_timeframe_notes(current_timeframe, market_regime)
            }

        except Exception as e:
            logger.error(f"Timeframe suitability analysis failed: {str(e)}")
            return {
                'current_timeframe': 'UNKNOWN',
                'suitability_scores': {},
                'recommended_style': 'day_trading',
                'style_confidence': 0.5,
                'timeframe_notes': []
            }

    def _generate_timeframe_notes(self, timeframe: str, market_regime: str) -> List[str]:
        """Generate notes about timeframe suitability"""
        notes = []

        timeframe_notes = {
            'M1': ['Cocok untuk scalping', 'Perlu reaksi cepat', 'Spread impact tinggi'],
            'M5': ['Baik untuk scalping dan day trading', 'Sinyal lebih reliable dari M1'],
            'M15': ['Timeframe populer untuk day trading', 'Balance antara noise dan signal'],
            'M30': ['Cocok untuk day trading dengan holding lebih lama'],
            'H1': ['Ideal untuk swing trading', 'Sinyal lebih reliable'],
            'H4': ['Excellent untuk swing trading', 'Trend lebih jelas'],
            'D1': ['Untuk position trading', 'Trend jangka panjang']
        }

        notes.extend(timeframe_notes.get(timeframe, ['Timeframe tidak dikenali']))

        # Add regime-specific notes
        if market_regime == 'volatile':
            notes.append('Market volatile - pertimbangkan timeframe lebih tinggi')
        elif market_regime in ['trending_bull', 'trending_bear']:
            notes.append('Trending market - timeframe lebih tinggi lebih reliable')

        return notes

    def _generate_enhanced_scenario_analysis(self, validated_signals: List[Dict],
                                           recommendation_analysis: Dict, market_regime: str) -> Dict[str, Any]:
        """Generate enhanced scenario analysis with probabilities"""
        try:
            recommendation = recommendation_analysis.get('recommendation', 'HOLD')

            scenarios = {
                'bullish': {
                    'probability': 0.0,
                    'description': '',
                    'key_factors': [],
                    'price_targets': [],
                    'conditions': []
                },
                'bearish': {
                    'probability': 0.0,
                    'description': '',
                    'key_factors': [],
                    'price_targets': [],
                    'conditions': []
                },
                'neutral': {
                    'probability': 0.0,
                    'description': '',
                    'key_factors': [],
                    'price_targets': [],
                    'conditions': []
                }
            }

            # Calculate probabilities based on signals
            buy_signals = [s for s in validated_signals if s.get('type') == 'BUY']
            sell_signals = [s for s in validated_signals if s.get('type') == 'SELL']

            total_buy_strength = sum(s.get('validation_score', 0) for s in buy_signals)
            total_sell_strength = sum(s.get('validation_score', 0) for s in sell_signals)
            total_strength = total_buy_strength + total_sell_strength

            if total_strength > 0:
                scenarios['bullish']['probability'] = total_buy_strength / total_strength
                scenarios['bearish']['probability'] = total_sell_strength / total_strength
                scenarios['neutral']['probability'] = 1.0 - scenarios['bullish']['probability'] - scenarios['bearish']['probability']
            else:
                scenarios['neutral']['probability'] = 1.0

            # Normalize probabilities
            total_prob = sum(s['probability'] for s in scenarios.values())
            if total_prob > 0:
                for scenario in scenarios.values():
                    scenario['probability'] /= total_prob

            # Generate descriptions and factors
            scenarios['bullish']['description'] = f"Skenario bullish dengan probabilitas {scenarios['bullish']['probability']:.1%}"
            scenarios['bullish']['key_factors'] = [s.get('source', 'Unknown') for s in buy_signals[:3]]
            scenarios['bullish']['conditions'] = ['Break above resistance', 'Volume confirmation', 'Momentum continuation']

            scenarios['bearish']['description'] = f"Skenario bearish dengan probabilitas {scenarios['bearish']['probability']:.1%}"
            scenarios['bearish']['key_factors'] = [s.get('source', 'Unknown') for s in sell_signals[:3]]
            scenarios['bearish']['conditions'] = ['Break below support', 'Volume confirmation', 'Momentum continuation']

            scenarios['neutral']['description'] = f"Skenario sideways dengan probabilitas {scenarios['neutral']['probability']:.1%}"
            scenarios['neutral']['key_factors'] = ['Mixed signals', 'Low momentum', 'Range-bound market']
            scenarios['neutral']['conditions'] = ['Respect of support/resistance', 'Low volume', 'Consolidation pattern']

            return scenarios

        except Exception as e:
            logger.error(f"Enhanced scenario analysis failed: {str(e)}")
            return {
                'bullish': {'probability': 0.33, 'description': 'Error in analysis'},
                'bearish': {'probability': 0.33, 'description': 'Error in analysis'},
                'neutral': {'probability': 0.34, 'description': 'Error in analysis'}
            }

    def _breakdown_signal_strength(self, validated_signals: List[Dict]) -> Dict[str, Any]:
        """Breakdown signal strength by source and type"""
        try:
            breakdown = {
                'by_source': {},
                'by_type': {},
                'total_signals': len(validated_signals),
                'average_strength': 0.0,
                'strongest_signal': None,
                'weakest_signal': None
            }

            if not validated_signals:
                return breakdown

            # Breakdown by source
            for signal in validated_signals:
                source = signal.get('source', 'unknown')
                strength = signal.get('validation_score', 0.0)

                if source not in breakdown['by_source']:
                    breakdown['by_source'][source] = {'count': 0, 'total_strength': 0.0, 'avg_strength': 0.0}

                breakdown['by_source'][source]['count'] += 1
                breakdown['by_source'][source]['total_strength'] += strength

            # Calculate averages for sources
            for source_data in breakdown['by_source'].values():
                source_data['avg_strength'] = source_data['total_strength'] / source_data['count']

            # Breakdown by type
            for signal in validated_signals:
                signal_type = signal.get('type', 'unknown')
                strength = signal.get('validation_score', 0.0)

                if signal_type not in breakdown['by_type']:
                    breakdown['by_type'][signal_type] = {'count': 0, 'total_strength': 0.0, 'avg_strength': 0.0}

                breakdown['by_type'][signal_type]['count'] += 1
                breakdown['by_type'][signal_type]['total_strength'] += strength

            # Calculate averages for types
            for type_data in breakdown['by_type'].values():
                type_data['avg_strength'] = type_data['total_strength'] / type_data['count']

            # Overall statistics
            strengths = [s.get('validation_score', 0.0) for s in validated_signals]
            breakdown['average_strength'] = sum(strengths) / len(strengths)
            breakdown['strongest_signal'] = max(validated_signals, key=lambda x: x.get('validation_score', 0.0))
            breakdown['weakest_signal'] = min(validated_signals, key=lambda x: x.get('validation_score', 0.0))

            return breakdown

        except Exception as e:
            logger.error(f"Signal strength breakdown failed: {str(e)}")
            return {
                'by_source': {},
                'by_type': {},
                'total_signals': 0,
                'average_strength': 0.0,
                'strongest_signal': None,
                'weakest_signal': None
            }

    def _summarize_validation_results(self, all_signals: List[Dict], validated_signals: List[Dict]) -> Dict[str, Any]:
        """Summarize validation results"""
        return {
            'total_signals_detected': len(all_signals),
            'signals_validated': len(validated_signals),
            'validation_rate': len(validated_signals) / len(all_signals) if all_signals else 0.0,
            'rejected_signals': len(all_signals) - len(validated_signals),
            'validation_summary': f"{len(validated_signals)} dari {len(all_signals)} sinyal lolos validasi"
        }

    def _generate_enhanced_default_decision(self, error_message: str) -> Dict[str, Any]:
        """Generate enhanced default decision for error cases"""
        return {
            'recommendation': 'HOLD',
            'confidence': 0.0,
            'confidence_level': 'very_low',
            'confidence_description': 'Sangat Rendah - Error dalam analisis',

            'entry_price': 0.0,
            'stop_loss': 0.0,
            'take_profit': 0.0,
            'risk_reward_ratio': 0.0,
            'position_size': 0.0,
            'max_risk_percent': 0.02,

            'market_regime': 'unknown',
            'market_context': {'error': error_message},

            'signal_reasoning': {
                'signal_explanations': [],
                'confluence_reasoning': 'Error dalam analisis confluence',
                'market_context_impact': 'Error dalam analisis konteks pasar',
                'strength_analysis': {},
                'risk_assessment': [{'factor': 'System error', 'impact': 'high', 'mitigation': 'Manual analysis required'}]
            },

            'confluence_analysis': {
                'confluence_score': 0.0,
                'confluence_level': 'none',
                'confluent_signals': [],
                'conflicting_signals': [],
                'signal_consensus': 'NEUTRAL'
            },

            'validated_signals': [],
            'signal_strength_breakdown': {},
            'supporting_factors': [],
            'risk_factors': ['System error occurred'],
            'key_levels': [],

            'execution_plan': {
                'action': 'HOLD',
                'execution_steps': ['Manual analysis required due to system error'],
                'timing_guidance': 'Manual analysis required',
                'entry_strategy': 'Manual analysis required',
                'exit_strategy': 'Manual analysis required',
                'monitoring_points': ['System recovery', 'Manual verification']
            },

            'timeframe_analysis': {
                'current_timeframe': 'UNKNOWN',
                'recommended_style': 'manual',
                'style_confidence': 0.0
            },

            'scenario_analysis': {
                'bullish': {'probability': 0.33, 'description': 'Unknown due to error'},
                'bearish': {'probability': 0.33, 'description': 'Unknown due to error'},
                'neutral': {'probability': 0.34, 'description': 'Default due to error'}
            },

            'weighted_scores': {'total_weighted_score': 0.0},
            'validation_results': {'validation_summary': 'Error in validation'},
            'analysis_timestamp': datetime.now().isoformat(),
            'analysis_version': '2.0_enhanced',
            'error_details': error_message
        }
    
    def _collect_all_signals(self, price_action: Dict, smc: Dict, technical: Dict) -> List[Dict[str, Any]]:
        """Collect all signals from different analysis modules"""
        all_signals = []
        
        # Price action signals
        pa_signals = price_action.get('signals', [])
        for signal in pa_signals:
            signal['category'] = 'price_action'
            all_signals.append(signal)
        
        # SMC signals
        smc_signals = smc.get('smc_signals', [])
        for signal in smc_signals:
            signal['category'] = 'smc'
            all_signals.append(signal)
        
        # Technical signals
        tech_signals = technical.get('technical_signals', [])
        for signal in tech_signals:
            signal['category'] = 'technical'
            all_signals.append(signal)
        
        return all_signals
    
    def _calculate_weighted_scores(self, price_action: Dict, smc: Dict, technical: Dict, ocr_data: Dict) -> Dict[str, float]:
        """Calculate weighted scores for each analysis type"""
        scores = {
            'price_action_score': price_action.get('price_action_score', 0.0),
            'smc_score': smc.get('smc_score', 0.0),
            'technical_score': technical.get('technical_score', 0.0),
            'ocr_confidence': ocr_data.get('extraction_confidence', 0.0)
        }
        
        # Calculate weighted total
        weighted_total = (
            scores['price_action_score'] * self.weights['price_action'] +
            scores['smc_score'] * self.weights['smc'] +
            scores['technical_score'] * self.weights['technical'] +
            scores['ocr_confidence'] * self.weights['ocr_confidence']
        )
        
        scores['weighted_total'] = weighted_total
        return scores
    
    def _determine_primary_recommendation(self, weighted_scores: Dict, all_signals: List) -> str:
        """Determine the primary trading recommendation"""
        if not all_signals:
            return 'HOLD'
        
        # Count signal types
        buy_signals = [s for s in all_signals if s.get('type') == 'BUY']
        sell_signals = [s for s in all_signals if s.get('type') == 'SELL']
        
        # Calculate signal strengths
        buy_strength = sum(s.get('strength', 0) * s.get('confidence', 0) for s in buy_signals)
        sell_strength = sum(s.get('strength', 0) * s.get('confidence', 0) for s in sell_signals)
        
        # Consider weighted total score
        score_threshold = 0.6
        
        if weighted_scores['weighted_total'] > score_threshold:
            if buy_strength > sell_strength * 1.2:
                return 'BUY'
            elif sell_strength > buy_strength * 1.2:
                return 'SELL'
        
        # If signals are mixed or weak, recommend HOLD
        return 'HOLD'
    
    def _calculate_confidence(self, weighted_scores: Dict, all_signals: List, ocr_data: Dict) -> float:
        """Calculate overall confidence in the recommendation"""
        if not all_signals:
            return 0.0
        
        # Base confidence from weighted scores
        base_confidence = weighted_scores['weighted_total']
        
        # Signal consensus factor
        signal_types = [s.get('type') for s in all_signals if s.get('type') in ['BUY', 'SELL']]
        if signal_types:
            most_common = max(set(signal_types), key=signal_types.count)
            consensus_ratio = signal_types.count(most_common) / len(signal_types)
        else:
            consensus_ratio = 0.0
        
        # OCR quality factor
        ocr_factor = ocr_data.get('extraction_confidence', 0.0)
        
        # Signal strength factor
        avg_signal_strength = sum(s.get('strength', 0) for s in all_signals) / len(all_signals)
        
        # Combine factors
        confidence = (
            base_confidence * 0.4 +
            consensus_ratio * 0.3 +
            ocr_factor * 0.1 +
            avg_signal_strength * 0.2
        )
        
        return min(confidence, 1.0)
    
    def _classify_confidence(self, confidence: float) -> str:
        """Classify confidence level"""
        if confidence >= self.confidence_thresholds['high']:
            return 'HIGH'
        elif confidence >= self.confidence_thresholds['medium']:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _calculate_entry_price(self, ocr_data: Dict, recommendation: str) -> float:
        """Calculate suggested entry price"""
        current_price = ocr_data.get('current_price', 0.0)
        
        if current_price == 0.0:
            # Try to get from price data
            price_data = ocr_data.get('price_data', {})
            current_price = price_data.get('current', price_data.get('close', 0.0))
        
        # For market orders, use current price
        # For limit orders, you might adjust based on recommendation
        if recommendation == 'BUY':
            # Slightly below current price for better entry
            return current_price * 0.999 if current_price > 0 else 0.0
        elif recommendation == 'SELL':
            # Slightly above current price for better entry
            return current_price * 1.001 if current_price > 0 else 0.0
        else:
            return current_price
    
    def _calculate_risk_management(self, recommendation: str, price_action: Dict, 
                                 smc: Dict, technical: Dict, ocr_data: Dict) -> Dict[str, Any]:
        """Calculate stop loss and take profit levels"""
        current_price = self._calculate_entry_price(ocr_data, recommendation)
        
        if current_price == 0.0:
            return {
                'stop_loss': 0.0,
                'take_profit': 0.0,
                'risk_reward_ratio': 0.0
            }
        
        # Get key levels for risk management
        pa_levels = price_action.get('key_levels', [])
        smc_zones = smc.get('key_zones', [])
        tech_levels = technical.get('support_resistance', {}).get('levels', [])
        
        if recommendation == 'BUY':
            # Stop loss below nearest support
            support_levels = [l for l in pa_levels if l.get('type') == 'support']
            if support_levels:
                nearest_support = min(support_levels, key=lambda l: abs(l.get('price_level', 0) - current_price))
                stop_loss = current_price - (current_price - nearest_support.get('price_level', current_price)) * 1.1
            else:
                stop_loss = current_price * 0.98  # 2% stop loss
            
            # Take profit at resistance or using risk-reward ratio
            resistance_levels = [l for l in pa_levels if l.get('type') == 'resistance']
            if resistance_levels:
                nearest_resistance = min(resistance_levels, key=lambda l: abs(l.get('price_level', 0) - current_price))
                take_profit = nearest_resistance.get('price_level', current_price * 1.04)
            else:
                risk_amount = current_price - stop_loss
                take_profit = current_price + (risk_amount * self.risk_reward_ratios['moderate'])
        
        elif recommendation == 'SELL':
            # Stop loss above nearest resistance
            resistance_levels = [l for l in pa_levels if l.get('type') == 'resistance']
            if resistance_levels:
                nearest_resistance = min(resistance_levels, key=lambda l: abs(l.get('price_level', 0) - current_price))
                stop_loss = current_price + (nearest_resistance.get('price_level', current_price) - current_price) * 1.1
            else:
                stop_loss = current_price * 1.02  # 2% stop loss
            
            # Take profit at support or using risk-reward ratio
            support_levels = [l for l in pa_levels if l.get('type') == 'support']
            if support_levels:
                nearest_support = min(support_levels, key=lambda l: abs(l.get('price_level', 0) - current_price))
                take_profit = nearest_support.get('price_level', current_price * 0.96)
            else:
                risk_amount = stop_loss - current_price
                take_profit = current_price - (risk_amount * self.risk_reward_ratios['moderate'])
        
        else:  # HOLD
            return {
                'stop_loss': 0.0,
                'take_profit': 0.0,
                'risk_reward_ratio': 0.0
            }
        
        # Calculate risk-reward ratio
        risk = abs(current_price - stop_loss)
        reward = abs(take_profit - current_price)
        risk_reward_ratio = reward / risk if risk > 0 else 0.0
        
        return {
            'stop_loss': round(stop_loss, 5),
            'take_profit': round(take_profit, 5),
            'risk_reward_ratio': round(risk_reward_ratio, 2)
        }
    
    def _calculate_position_size(self, confidence: float) -> str:
        """Calculate suggested position size based on confidence"""
        if confidence >= self.confidence_thresholds['high']:
            return 'STANDARD'  # 1-2% risk
        elif confidence >= self.confidence_thresholds['medium']:
            return 'REDUCED'   # 0.5-1% risk
        else:
            return 'MINIMAL'   # 0.25-0.5% risk
    
    def _assess_timeframe_suitability(self, ocr_data: Dict, all_signals: List) -> Dict[str, Any]:
        """Assess suitability for different timeframes"""
        timeframe = ocr_data.get('timeframe', 'UNKNOWN')
        
        # Count signal sources
        signal_sources = set(s.get('source', '') for s in all_signals)
        
        suitability = {
            'current_timeframe': timeframe,
            'scalping': 'LOW',
            'day_trading': 'MEDIUM',
            'swing_trading': 'HIGH',
            'position_trading': 'MEDIUM'
        }
        
        # Adjust based on signal types and timeframe
        if timeframe in ['M1', 'M5', '1m', '5m']:
            suitability['scalping'] = 'HIGH'
            suitability['day_trading'] = 'HIGH'
            suitability['swing_trading'] = 'LOW'
        elif timeframe in ['M15', 'M30', 'H1', '15m', '30m', '1h']:
            suitability['day_trading'] = 'HIGH'
            suitability['swing_trading'] = 'MEDIUM'
        elif timeframe in ['H4', 'D1', '4h', '1d']:
            suitability['swing_trading'] = 'HIGH'
            suitability['position_trading'] = 'HIGH'
        
        return suitability
    
    def _identify_key_decision_levels(self, price_action: Dict, smc: Dict, technical: Dict) -> List[Dict[str, Any]]:
        """Identify key levels that influenced the decision"""
        key_levels = []
        
        # From price action
        pa_levels = price_action.get('key_levels', [])[:3]  # Top 3
        for level in pa_levels:
            key_levels.append({
                'source': 'Price Action',
                'type': level.get('type', 'level'),
                'value': level.get('price_level', 0),
                'strength': level.get('strength', 0)
            })
        
        # From SMC
        smc_zones = smc.get('key_zones', [])[:2]  # Top 2
        for zone in smc_zones:
            key_levels.append({
                'source': 'SMC',
                'type': zone.get('type', 'zone'),
                'value': zone.get('y_position', zone.get('location', {}).get('y', 0)),
                'strength': zone.get('strength', 0)
            })
        
        # From technical analysis
        tech_sr = technical.get('support_resistance', {})
        if tech_sr.get('key_support'):
            key_levels.append({
                'source': 'Technical',
                'type': 'support',
                'value': tech_sr['key_support'].get('price_level', 0),
                'strength': tech_sr['key_support'].get('strength', 0)
            })
        
        if tech_sr.get('key_resistance'):
            key_levels.append({
                'source': 'Technical',
                'type': 'resistance',
                'value': tech_sr['key_resistance'].get('price_level', 0),
                'strength': tech_sr['key_resistance'].get('strength', 0)
            })
        
        return key_levels
    
    def _identify_supporting_factors(self, all_signals: List, recommendation: str) -> List[str]:
        """Identify factors supporting the recommendation"""
        supporting_factors = []
        
        # Count supporting signals by category
        supporting_signals = [s for s in all_signals if s.get('type') == recommendation]
        
        categories = {}
        for signal in supporting_signals:
            category = signal.get('category', 'unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append(signal)
        
        # Generate supporting factor descriptions
        for category, signals in categories.items():
            if len(signals) > 0:
                avg_strength = sum(s.get('strength', 0) for s in signals) / len(signals)
                supporting_factors.append(f"{category.replace('_', ' ').title()}: {len(signals)} signal(s) with avg strength {avg_strength:.2f}")
        
        return supporting_factors
    
    def _identify_risk_factors(self, all_signals: List, recommendation: str) -> List[str]:
        """Identify risk factors against the recommendation"""
        risk_factors = []
        
        # Count opposing signals
        opposing_type = 'SELL' if recommendation == 'BUY' else 'BUY'
        opposing_signals = [s for s in all_signals if s.get('type') == opposing_type]
        
        if opposing_signals:
            risk_factors.append(f"{len(opposing_signals)} opposing signal(s) detected")
        
        # Check for mixed signals within categories
        categories = {}
        for signal in all_signals:
            category = signal.get('category', 'unknown')
            if category not in categories:
                categories[category] = []
            categories[category].append(signal.get('type'))
        
        for category, signal_types in categories.items():
            unique_types = set(signal_types)
            if len(unique_types) > 1:
                risk_factors.append(f"Mixed signals in {category.replace('_', ' ')}")
        
        return risk_factors
    
    def _generate_scenarios(self, weighted_scores: Dict, all_signals: List) -> Dict[str, Dict]:
        """Generate alternative scenarios"""
        scenarios = {
            'bullish': {'probability': 0.0, 'factors': []},
            'bearish': {'probability': 0.0, 'factors': []},
            'neutral': {'probability': 0.0, 'factors': []}
        }
        
        # Calculate probabilities based on signals
        buy_signals = [s for s in all_signals if s.get('type') == 'BUY']
        sell_signals = [s for s in all_signals if s.get('type') == 'SELL']
        hold_signals = [s for s in all_signals if s.get('type') == 'HOLD']
        
        total_signals = len(all_signals)
        if total_signals > 0:
            scenarios['bullish']['probability'] = len(buy_signals) / total_signals
            scenarios['bearish']['probability'] = len(sell_signals) / total_signals
            scenarios['neutral']['probability'] = len(hold_signals) / total_signals
        
        # Add factors for each scenario
        scenarios['bullish']['factors'] = [s.get('source', 'Unknown') for s in buy_signals]
        scenarios['bearish']['factors'] = [s.get('source', 'Unknown') for s in sell_signals]
        scenarios['neutral']['factors'] = ['Mixed signals', 'Low confidence'] if total_signals > 0 else ['No clear signals']
        
        return scenarios
    
    def _analyze_market_context(self, price_action: Dict, smc: Dict, technical: Dict) -> Dict[str, str]:
        """Analyze overall market context"""
        return {
            'trend': price_action.get('trend', 'NEUTRAL'),
            'structure': smc.get('market_structure', 'NEUTRAL'),
            'momentum': technical.get('overall_momentum', 'NEUTRAL'),
            'volatility': 'NORMAL'  # Could be enhanced with volatility analysis
        }
    
    def _generate_execution_notes(self, recommendation: str, confidence: float) -> List[str]:
        """Generate execution notes and tips"""
        notes = []
        
        if recommendation == 'HOLD':
            notes.append("Wait for clearer signals before entering a position")
            notes.append("Monitor key levels for potential breakouts")
        else:
            if confidence >= self.confidence_thresholds['high']:
                notes.append("High confidence setup - consider standard position size")
            elif confidence >= self.confidence_thresholds['medium']:
                notes.append("Medium confidence - consider reduced position size")
            else:
                notes.append("Low confidence - consider minimal position size or wait")
            
            notes.append("Use proper risk management with stop loss")
            notes.append("Consider scaling out at key resistance/support levels")
        
        return notes
    
    def _summarize_signals(self, all_signals: List) -> Dict[str, Any]:
        """Summarize all signals for reporting"""
        summary = {
            'total_signals': len(all_signals),
            'buy_signals': len([s for s in all_signals if s.get('type') == 'BUY']),
            'sell_signals': len([s for s in all_signals if s.get('type') == 'SELL']),
            'hold_signals': len([s for s in all_signals if s.get('type') == 'HOLD']),
            'categories': {}
        }
        
        # Group by category
        for signal in all_signals:
            category = signal.get('category', 'unknown')
            if category not in summary['categories']:
                summary['categories'][category] = 0
            summary['categories'][category] += 1
        
        return summary
    
    def _generate_default_decision(self) -> Dict[str, Any]:
        """Generate default decision when analysis fails"""
        return {
            'recommendation': 'HOLD',
            'confidence': 0.0,
            'confidence_level': 'LOW',
            'entry_price': 0.0,
            'stop_loss': 0.0,
            'take_profit': 0.0,
            'risk_reward_ratio': 0.0,
            'position_size': 'MINIMAL',
            'timeframe_suitability': {},
            'key_levels': [],
            'supporting_factors': [],
            'risk_factors': ['Analysis failed'],
            'scenarios': {},
            'market_context': {},
            'execution_notes': ['Unable to analyze - avoid trading'],
            'weighted_scores': {},
            'signal_summary': {'total_signals': 0}
        }

    def _process_user_context(self, user_context: str, ocr_data: Dict) -> Dict[str, Any]:
        """
        Process user context to extract trading bias and corrections

        Args:
            user_context (str): User provided context/corrections
            ocr_data (Dict): OCR data with potential user corrections

        Returns:
            Dict: User bias and context information
        """
        try:
            logger.info(f"Processing user context: {user_context}")

            context_lower = user_context.lower()
            user_bias = {
                'direction': None,
                'confidence': 0.0,
                'pattern': None,
                'corrections': [],
                'context_weight': 0.2  # Weight for user input in final decision
            }

            # Extract directional bias
            bullish_keywords = ['bullish', 'buy', 'long', 'uptrend', 'up', 'higher', 'rise']
            bearish_keywords = ['bearish', 'sell', 'short', 'downtrend', 'down', 'lower', 'fall']

            bullish_count = sum(1 for keyword in bullish_keywords if keyword in context_lower)
            bearish_count = sum(1 for keyword in bearish_keywords if keyword in context_lower)

            if bullish_count > bearish_count:
                user_bias['direction'] = 'BULLISH'
                user_bias['confidence'] = min(0.8, 0.3 + (bullish_count * 0.1))
            elif bearish_count > bullish_count:
                user_bias['direction'] = 'BEARISH'
                user_bias['confidence'] = min(0.8, 0.3 + (bearish_count * 0.1))
            else:
                user_bias['direction'] = 'NEUTRAL'
                user_bias['confidence'] = 0.3

            # Extract pattern information
            patterns = {
                'double top': 'BEARISH_REVERSAL',
                'double bottom': 'BULLISH_REVERSAL',
                'head and shoulders': 'BEARISH_REVERSAL',
                'inverse head and shoulders': 'BULLISH_REVERSAL',
                'triangle': 'CONTINUATION',
                'flag': 'CONTINUATION',
                'pennant': 'CONTINUATION',
                'wedge': 'REVERSAL',
                'channel': 'RANGE'
            }

            for pattern_name, pattern_type in patterns.items():
                if pattern_name in context_lower:
                    user_bias['pattern'] = {
                        'name': pattern_name.title(),
                        'type': pattern_type
                    }
                    break

            # Check for corrections in OCR data
            if 'user_trend' in ocr_data:
                user_bias['corrections'].append(f"Trend: {ocr_data['user_trend']}")

            if 'user_pattern' in ocr_data:
                user_bias['corrections'].append(f"Pattern: {ocr_data['user_pattern']}")

            if 'user_context' in ocr_data:
                user_bias['corrections'].append(f"Context: {ocr_data['user_context']}")

            logger.info(f"User bias extracted: {user_bias}")
            return user_bias

        except Exception as e:
            logger.error(f"Error processing user context: {str(e)}")
            return {
                'direction': 'NEUTRAL',
                'confidence': 0.0,
                'pattern': None,
                'corrections': [],
                'context_weight': 0.0
            }
