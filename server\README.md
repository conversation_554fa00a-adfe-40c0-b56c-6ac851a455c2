# Market Analysis Server

Backend API server untuk aplikasi Market Trend Analysis menggunakan Express.js, MySQL, dan Python integration.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 atau lebih tinggi)
- MySQL Server
- Python 3.x (untuk analysis scripts)

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Setup environment variables:**
   ```bash
   cp .env.example .env
   ```
   Edit file `.env` sesuai dengan konfigurasi database Anda.

3. **Setup database:**
   - Buat database MySQL dengan nama `market_analysis`
   - Import schema database (jika ada file SQL schema)

4. **Start server:**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

Server akan berjalan di `http://localhost:5000`

## 📁 Project Structure

```
server/
├── app.js                 # Main application file
├── package.json          # Dependencies dan scripts
├── .env.example          # Environment variables template
├── .gitignore           # Git ignore rules
├── config/
│   └── database.js      # Database configuration
├── middleware/
│   ├── errorHandler.js  # Error handling middleware
│   └── logger.js        # Logging middleware
├── models/
│   ├── Analysis.js      # Analysis data model
│   ├── OCRData.js       # OCR data model
│   └── Upload.js        # Upload data model
├── routes/
│   ├── analysis.js      # Analysis endpoints
│   ├── stats.js         # Statistics endpoints
│   └── upload.js        # Upload endpoints
├── uploads/             # Uploaded files (auto-created)
└── logs/               # Application logs (auto-created)
```

## 🔧 API Endpoints

### Health Check
- `GET /health` - Server health status

### Upload
- `POST /api/upload` - Upload chart image

### Analysis
- `POST /api/analysis/initial-read/:uploadId` - Start initial chart reading
- `GET /api/analysis/initial-read/:uploadId` - Get initial reading results
- `POST /api/analysis/final-analyze/:uploadId` - Start final analysis
- `GET /api/analysis/upload/:uploadId` - Get final analysis results

### Statistics
- `GET /api/stats` - Get application statistics

## 🗄️ Database

Aplikasi menggunakan MySQL dengan schema `market_analysis`. Tables utama:
- `uploads` - Data file yang diupload
- `initial_readings` - Hasil pembacaan awal chart
- `analyses` - Hasil analisis final

## 🐍 Python Integration

Server mengintegrasikan Python scripts untuk analisis chart:
- Path: `../python_analysis/main.py`
- Mode: `initial` untuk pembacaan awal, `full` untuk analisis lengkap

## 🔒 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 5000 |
| `NODE_ENV` | Environment mode | development |
| `DB_HOST` | Database host | localhost |
| `DB_PORT` | Database port | 3306 |
| `DB_NAME` | Database name | market_analysis |
| `DB_USER` | Database user | root |
| `DB_PASSWORD` | Database password | - |
| `UPLOAD_DIR` | Upload directory | uploads |
| `MAX_FILE_SIZE` | Max file size (bytes) | 10485760 |
| `PYTHON_SCRIPT_PATH` | Python script path | ../python_analysis/main.py |
| `PYTHON_ENV` | Python command | python |

## 📝 Logging

- Access logs: `logs/access.log`
- Error logs: Console output dengan timestamp
- Request/Response logging dengan custom middleware

## 🛡️ Error Handling

- Custom error handler middleware
- Structured error responses
- Database connection error handling
- File upload validation

## 🔄 Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Check logs
tail -f logs/access.log
```

## 🚀 Production Deployment

1. Set `NODE_ENV=production` di `.env`
2. Configure production database
3. Setup reverse proxy (nginx/apache)
4. Use process manager (PM2)

```bash
# Using PM2
npm install -g pm2
pm2 start app.js --name "market-analysis-api"
```

## 🧪 Testing

Server menyediakan health check endpoint untuk monitoring:
```bash
curl http://localhost:5000/health
```

## 📋 Dependencies

### Production
- `express` - Web framework
- `cors` - CORS middleware
- `mysql2` - MySQL client
- `multer` - File upload handling
- `dotenv` - Environment variables
- `uuid` - UUID generation

### Development
- Custom middleware untuk logging dan error handling
- Database connection pooling
- File validation dan processing
