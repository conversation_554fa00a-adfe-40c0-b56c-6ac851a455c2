'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Eye,
  Edit3,
  ArrowRight,
  RefreshCw
} from 'lucide-react';
import axios from 'axios';

interface MultiStepAnalysisProps {
  onAnalysisComplete: (data: any) => void;
  onReset: () => void;
}

type AnalysisStep = 'upload' | 'reading' | 'summary' | 'correction' | 'analyzing' | 'complete';

interface InitialSummary {
  summary: string;
  ocr_data: {
    symbol: string;
    timeframe: string;
    current_price: number;
    extracted_text: string;
    price_count: number;
  };
  basic_patterns: {
    trend: string;
    pattern: string;
    price_range: {
      high: number;
      low: number;
    };
  };
}

export default function MultiStepAnalysis({ 
  onAnalysisComplete, 
  onReset 
}: MultiStepAnalysisProps) {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<AnalysisStep>('upload');
  const [uploadId, setUploadId] = useState<string | null>(null);
  const [initialSummary, setInitialSummary] = useState<InitialSummary | null>(null);
  const [userCorrection, setUserCorrection] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
      setCurrentStep('upload');
      setErrorMessage('');
      setInitialSummary(null);
      setUserCorrection('');
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']
    },
    maxFiles: 1,
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  const handleStartAnalysis = async () => {
    if (!uploadedFile) return;

    setCurrentStep('reading');
    setErrorMessage('');

    try {
      // Step 1: Upload file
      const formData = new FormData();
      formData.append('image', uploadedFile);

      const uploadResponse = await axios.post('http://localhost:5000/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (uploadResponse.data.success) {
        const newUploadId = uploadResponse.data.data.id;
        setUploadId(newUploadId);

        // Step 2: Start initial reading
        const readingResponse = await axios.post(`http://localhost:5000/api/analysis/initial-read/${newUploadId}`);

        if (readingResponse.data.success) {
          // Poll for initial reading results
          pollForInitialReading(newUploadId);
        } else {
          throw new Error('Gagal memulai pembacaan gambar');
        }
      } else {
        throw new Error('Upload gagal');
      }
    } catch (error: any) {
      console.error('Error starting analysis:', error);
      setCurrentStep('upload');
      setErrorMessage(error.response?.data?.message || error.message || 'Terjadi kesalahan');
    }
  };

  const pollForInitialReading = async (uploadId: string) => {
    const maxAttempts = 30; // 2.5 minutes with 5-second intervals
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const response = await axios.get(`http://localhost:5000/api/analysis/initial-read/${uploadId}`);

        if (response.data.success && response.data.data) {
          // Initial reading completed
          setInitialSummary(response.data.data);
          setCurrentStep('summary');
        } else if (response.data.status === 'processing') {
          // Still processing, continue polling
          if (attempts < maxAttempts) {
            setTimeout(poll, 5000);
          } else {
            throw new Error('Timeout: Pembacaan gambar terlalu lama');
          }
        } else {
          throw new Error('Gagal membaca gambar');
        }
      } catch (error: any) {
        console.error('Polling error:', error);
        setCurrentStep('upload');
        setErrorMessage(error.message || 'Terjadi kesalahan saat membaca gambar');
      }
    };

    poll();
  };

  const handleConfirmSummary = () => {
    setCurrentStep('analyzing');
    startFinalAnalysis('');
  };

  const handleRequestCorrection = () => {
    setCurrentStep('correction');
  };

  const handleSubmitCorrection = () => {
    if (!userCorrection.trim()) {
      setErrorMessage('Mohon masukkan koreksi atau konteks tambahan');
      return;
    }
    
    setCurrentStep('analyzing');
    startFinalAnalysis(userCorrection);
  };

  const startFinalAnalysis = async (userPrompt: string) => {
    if (!uploadId) return;

    try {
      const analysisResponse = await axios.post(`http://localhost:5000/api/analysis/final-analyze/${uploadId}`, {
        user_prompt: userPrompt
      });

      if (analysisResponse.data.success) {
        // Poll for final results
        pollForFinalResults(uploadId);
      } else {
        throw new Error('Gagal memulai analisis final');
      }
    } catch (error: any) {
      console.error('Final analysis error:', error);
      setCurrentStep('summary');
      setErrorMessage(error.response?.data?.message || error.message || 'Gagal memulai analisis');
    }
  };

  const pollForFinalResults = async (uploadId: string) => {
    const maxAttempts = 60; // 5 minutes with 5-second intervals
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const response = await axios.get(`http://localhost:5000/api/analysis/upload/${uploadId}`);

        if (response.data.success && response.data.data) {
          // Analysis completed
          setCurrentStep('complete');
          onAnalysisComplete(response.data.data);
        } else if (response.data.status === 'processing') {
          // Still processing, continue polling
          if (attempts < maxAttempts) {
            setTimeout(poll, 5000);
          } else {
            throw new Error('Timeout: Analisis terlalu lama');
          }
        } else {
          throw new Error('Analisis gagal');
        }
      } catch (error: any) {
        console.error('Final polling error:', error);
        setCurrentStep('summary');
        setErrorMessage(error.message || 'Terjadi kesalahan saat analisis');
      }
    };

    poll();
  };

  const handleReset = () => {
    setUploadedFile(null);
    setPreviewUrl(null);
    setCurrentStep('upload');
    setUploadId(null);
    setInitialSummary(null);
    setUserCorrection('');
    setErrorMessage('');
    onReset();
  };

  const renderStepIndicator = () => {
    const steps = [
      { key: 'upload', label: 'Upload', icon: Upload },
      { key: 'reading', label: 'Membaca', icon: Eye },
      { key: 'summary', label: 'Ringkasan', icon: CheckCircle },
      { key: 'analyzing', label: 'Analisis', icon: RefreshCw },
      { key: 'complete', label: 'Selesai', icon: CheckCircle }
    ];

    const stepIndex = steps.findIndex(step => step.key === currentStep);

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = index <= stepIndex;
          const isCurrent = index === stepIndex;

          return (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
                ${isActive 
                  ? 'bg-blue-500 border-blue-500 text-white' 
                  : 'bg-gray-100 border-gray-300 text-gray-400'
                }
                ${isCurrent && (currentStep === 'reading' || currentStep === 'analyzing') 
                  ? 'animate-pulse' 
                  : ''
                }
              `}>
                <Icon size={16} />
              </div>
              
              <span className={`
                ml-2 text-sm font-medium transition-colors duration-300
                ${isActive ? 'text-blue-600' : 'text-gray-400'}
              `}>
                {step.label}
              </span>

              {index < steps.length - 1 && (
                <ArrowRight 
                  size={16} 
                  className={`mx-4 transition-colors duration-300 ${
                    isActive ? 'text-blue-400' : 'text-gray-300'
                  }`} 
                />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      {renderStepIndicator()}

      {errorMessage && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="text-red-500 mr-3" size={20} />
          <span className="text-red-700">{errorMessage}</span>
        </div>
      )}

      {/* Upload Step */}
      {currentStep === 'upload' && (
        <div className="space-y-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300
              ${isDragActive 
                ? 'border-blue-400 bg-blue-50' 
                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
              }
            `}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center">
              <Upload className="text-gray-400 mb-4" size={48} />
              <p className="text-lg font-medium text-gray-700 mb-2">
                {isDragActive ? 'Lepaskan file di sini...' : 'Drag & drop gambar chart atau klik untuk pilih'}
              </p>
              <p className="text-sm text-gray-500">
                Mendukung PNG, JPG, JPEG, GIF, BMP, WebP (max 10MB)
              </p>
            </div>
          </div>

          {previewUrl && (
            <div className="space-y-4">
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full max-h-96 object-contain rounded-lg border border-gray-200"
                />
                <button
                  onClick={() => {
                    setUploadedFile(null);
                    setPreviewUrl(null);
                  }}
                  className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                >
                  <X size={16} />
                </button>
              </div>

              <button
                onClick={handleStartAnalysis}
                className="w-full bg-blue-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-600 transition-colors flex items-center justify-center"
              >
                <Eye className="mr-2" size={20} />
                Mulai Analisis Chart
              </button>
            </div>
          )}
        </div>
      )}

      {/* Reading Step */}
      {currentStep === 'reading' && (
        <div className="text-center space-y-6">
          <div className="flex flex-col items-center">
            <div className="relative">
              <Loader2 className="animate-spin text-blue-500 mb-4" size={48} />
              <div className="absolute inset-0 flex items-center justify-center">
                <Eye className="text-blue-600" size={24} />
              </div>
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Sedang Membaca Chart...
            </h3>
            <p className="text-gray-600">
              Sistem sedang menganalisis dan membaca informasi dari gambar chart Anda
            </p>
          </div>

          {previewUrl && (
            <div className="max-w-md mx-auto">
              <img
                src={previewUrl}
                alt="Chart being analyzed"
                className="w-full rounded-lg border border-gray-200 opacity-75"
              />
            </div>
          )}
        </div>
      )}

      {/* Summary Step */}
      {currentStep === 'summary' && initialSummary && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-blue-800 mb-4 flex items-center">
              <CheckCircle className="mr-2" size={24} />
              Hasil Pembacaan Chart
            </h3>

            <div className="space-y-4">
              <div className="bg-white rounded-lg p-4 border border-blue-100">
                <h4 className="font-medium text-gray-800 mb-2">Ringkasan:</h4>
                <p className="text-gray-700 whitespace-pre-line leading-relaxed">
                  {initialSummary.summary}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white rounded-lg p-4 border border-blue-100">
                  <h4 className="font-medium text-gray-800 mb-2">Informasi Dasar:</h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li><strong>Symbol:</strong> {initialSummary.ocr_data.symbol}</li>
                    <li><strong>Timeframe:</strong> {initialSummary.ocr_data.timeframe}</li>
                    <li><strong>Current Price:</strong> {initialSummary.ocr_data.current_price.toFixed(5)}</li>
                    <li><strong>Data Points:</strong> {initialSummary.ocr_data.price_count}</li>
                  </ul>
                </div>

                <div className="bg-white rounded-lg p-4 border border-blue-100">
                  <h4 className="font-medium text-gray-800 mb-2">Pattern Analysis:</h4>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li><strong>Trend:</strong> {initialSummary.basic_patterns.trend}</li>
                    <li><strong>Pattern:</strong> {initialSummary.basic_patterns.pattern}</li>
                    <li><strong>High:</strong> {initialSummary.basic_patterns.price_range.high.toFixed(5)}</li>
                    <li><strong>Low:</strong> {initialSummary.basic_patterns.price_range.low.toFixed(5)}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleConfirmSummary}
              className="flex-1 bg-green-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-600 transition-colors flex items-center justify-center"
            >
              <CheckCircle className="mr-2" size={20} />
              Ya, Informasi Sudah Benar
            </button>

            <button
              onClick={handleRequestCorrection}
              className="flex-1 bg-orange-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-orange-600 transition-colors flex items-center justify-center"
            >
              <Edit3 className="mr-2" size={20} />
              Perlu Koreksi
            </button>
          </div>
        </div>
      )}

      {/* Correction Step */}
      {currentStep === 'correction' && (
        <div className="space-y-6">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-orange-800 mb-4 flex items-center">
              <Edit3 className="mr-2" size={24} />
              Koreksi & Konteks Tambahan
            </h3>

            <p className="text-orange-700 mb-4">
              Mohon berikan koreksi atau informasi tambahan yang sesuai dengan chart Anda:
            </p>

            <textarea
              value={userCorrection}
              onChange={(e) => setUserCorrection(e.target.value)}
              placeholder="Contoh: Symbol seharusnya EURUSD, timeframe H1, trend saat ini bullish, ada pattern double bottom di area support..."
              className="w-full h-32 p-4 border border-orange-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />

            <div className="mt-4 text-sm text-orange-600">
              <p><strong>Tips:</strong></p>
              <ul className="list-disc list-inside space-y-1 mt-2">
                <li>Sebutkan symbol yang benar (contoh: EURUSD, XAUUSD)</li>
                <li>Koreksi timeframe jika salah (M1, M5, M15, M30, H1, H4, D1)</li>
                <li>Jelaskan trend yang Anda lihat (bullish, bearish, sideways)</li>
                <li>Sebutkan pattern khusus yang Anda identifikasi</li>
                <li>Berikan konteks market condition saat ini</li>
              </ul>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={() => setCurrentStep('summary')}
              className="flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-600 transition-colors"
            >
              Kembali
            </button>

            <button
              onClick={handleSubmitCorrection}
              disabled={!userCorrection.trim()}
              className="flex-1 bg-blue-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              <ArrowRight className="mr-2" size={20} />
              Lanjutkan Analisis
            </button>
          </div>
        </div>
      )}

      {/* Analyzing Step */}
      {currentStep === 'analyzing' && (
        <div className="text-center space-y-6">
          <div className="flex flex-col items-center">
            <div className="relative">
              <Loader2 className="animate-spin text-blue-500 mb-4" size={48} />
              <div className="absolute inset-0 flex items-center justify-center">
                <RefreshCw className="text-blue-600" size={24} />
              </div>
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Sedang Menganalisis Chart...
            </h3>
            <p className="text-gray-600 mb-4">
              Sistem sedang melakukan analisis mendalam berdasarkan informasi yang telah dikonfirmasi
            </p>

            {userCorrection && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md">
                <p className="text-sm text-blue-700">
                  <strong>Konteks tambahan:</strong> {userCorrection}
                </p>
              </div>
            )}
          </div>

          {previewUrl && (
            <div className="max-w-md mx-auto">
              <img
                src={previewUrl}
                alt="Chart being analyzed"
                className="w-full rounded-lg border border-gray-200 opacity-75"
              />
            </div>
          )}
        </div>
      )}

      {/* Reset button for all steps except upload */}
      {currentStep !== 'upload' && currentStep !== 'complete' && (
        <div className="mt-8 text-center">
          <button
            onClick={handleReset}
            className="text-gray-500 hover:text-gray-700 transition-colors text-sm"
          >
            Mulai Ulang
          </button>
        </div>
      )}
    </div>
  );
}
