"""
OCR Extraction Module
Extracts text, prices, and metadata from trading chart images
"""

import cv2
import numpy as np
import pytesseract
import re
import logging
import os
from typing import Dict, List, Optional, Tuple, Any

# Configure Tesseract path for Windows
if os.name == 'nt':  # Windows
    tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
    if os.path.exists(tesseract_path):
        pytesseract.pytesseract.tesseract_cmd = tesseract_path

logger = logging.getLogger(__name__)

class OCRExtractor:
    """Handles OCR extraction and text processing for trading charts"""
    
    def __init__(self):
        
        # Common trading symbols
        self.trading_symbols = [
            'XAUUSD', 'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
            'EURJPY', 'GBPJPY', 'AUDJPY', 'CHFJPY', 'EURGBP', 'EURAUD', 'EURCHF', 'GBPAUD',
            'GBPC<PERSON>', 'AUDCHF', 'AUDCAD', 'CADCHF', 'NZDCHF', 'NZDJPY', 'BTCUSD', 'ETHUSD',
            'SPX500', 'US30', 'NAS100', 'GER30', 'UK100', 'JPN225', 'AUS200', 'FRA40',
            'OIL', 'GOLD', 'SILVER', 'COPPER'
        ]
        
        # Common timeframes
        self.timeframes = [
            'M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN1',
            '1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'
        ]
    
    def extract_data(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Main OCR extraction function
        
        Args:
            image (np.ndarray): Processed chart image
            
        Returns:
            Dict: Extracted data including text, prices, symbol, timeframe
        """
        try:
            logger.info("Starting OCR extraction...")
            
            # Prepare image for OCR
            ocr_image = self._prepare_for_ocr(image)
            
            # Extract raw text
            raw_text = self._extract_raw_text(ocr_image)
            
            # Process and clean text
            cleaned_text = self._clean_text(raw_text)
            
            # Extract specific data
            symbol = self._extract_symbol(cleaned_text)
            timeframe = self._extract_timeframe(cleaned_text)
            prices = self._extract_prices(cleaned_text)
            current_price = self._extract_current_price(cleaned_text, prices)
            
            # Extract chart metadata
            metadata = self._extract_metadata(cleaned_text)
            
            result = {
                "text": cleaned_text,
                "raw_text": raw_text,
                "symbol": symbol,
                "timeframe": timeframe,
                "current_price": current_price,
                "price_data": prices,
                "metadata": metadata,
                "extraction_confidence": self._calculate_confidence(symbol, timeframe, prices)
            }
            
            logger.info(f"OCR extraction completed. Symbol: {symbol}, Timeframe: {timeframe}")
            return result
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {str(e)}")
            return {
                "text": "",
                "raw_text": "",
                "symbol": "UNKNOWN",
                "timeframe": "UNKNOWN",
                "current_price": 0.0,
                "price_data": {},
                "metadata": {},
                "extraction_confidence": 0.0
            }
    
    def _prepare_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """Prepare image specifically for OCR"""
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Apply threshold
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to clean up text
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # Scale up for better OCR
        scaled = cv2.resize(cleaned, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
        
        return scaled
    
    def _extract_raw_text(self, image: np.ndarray) -> str:
        """Extract raw text using Tesseract OCR"""
        try:
            # Configure Tesseract
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,:/-+%$€£¥'
            
            # Extract text
            text = pytesseract.image_to_string(image, config=config)
            
            return text
            
        except Exception as e:
            logger.error(f"Tesseract OCR failed: {str(e)}")
            return ""
    
    def _clean_text(self, raw_text: str) -> str:
        """Clean and normalize extracted text"""
        if not raw_text:
            return ""
        
        # Remove extra whitespace and newlines
        cleaned = re.sub(r'\s+', ' ', raw_text.strip())
        
        # Remove common OCR artifacts
        cleaned = re.sub(r'[|\\]', '', cleaned)
        
        # Fix common OCR mistakes
        replacements = {
            '0': 'O',  # Sometimes O is read as 0
            'l': '1',  # Sometimes 1 is read as l
            'S': '5',  # Sometimes 5 is read as S
        }
        
        # Apply replacements selectively (only in price contexts)
        # This is a simplified approach - in production, you'd want more sophisticated logic
        
        return cleaned
    
    def _extract_symbol(self, text: str) -> str:
        """Extract trading symbol from text"""
        text_upper = text.upper()
        
        # Look for exact matches first
        for symbol in self.trading_symbols:
            if symbol in text_upper:
                logger.info(f"Found symbol: {symbol}")
                return symbol
        
        # Look for partial matches or variations
        # This is a simplified approach - you might want more sophisticated matching
        for symbol in self.trading_symbols:
            if len(symbol) >= 6:  # For pairs like XAUUSD
                if symbol[:3] in text_upper and symbol[3:] in text_upper:
                    logger.info(f"Found symbol (partial match): {symbol}")
                    return symbol
        
        logger.warning("No trading symbol found in text")
        return "UNKNOWN"
    
    def _extract_timeframe(self, text: str) -> str:
        """Extract timeframe from text"""
        text_upper = text.upper()
        
        # Look for exact matches
        for tf in self.timeframes:
            if tf.upper() in text_upper:
                logger.info(f"Found timeframe: {tf}")
                return tf
        
        # Look for patterns like "1 HOUR", "5 MIN", etc.
        time_patterns = [
            (r'1\s*MIN', 'M1'),
            (r'5\s*MIN', 'M5'),
            (r'15\s*MIN', 'M15'),
            (r'30\s*MIN', 'M30'),
            (r'1\s*HOUR', 'H1'),
            (r'4\s*HOUR', 'H4'),
            (r'1\s*DAY', 'D1'),
            (r'1\s*WEEK', 'W1'),
            (r'1\s*MONTH', 'MN1')
        ]
        
        for pattern, timeframe in time_patterns:
            if re.search(pattern, text_upper):
                logger.info(f"Found timeframe (pattern match): {timeframe}")
                return timeframe
        
        logger.warning("No timeframe found in text")
        return "UNKNOWN"
    
    def _extract_prices(self, text: str) -> Dict[str, float]:
        """Extract price values from text"""
        prices = {}
        
        # Pattern for decimal numbers (prices)
        price_pattern = r'\b\d+\.\d{2,5}\b'
        matches = re.findall(price_pattern, text)
        
        if matches:
            # Convert to floats and sort
            price_values = [float(match) for match in matches]
            price_values.sort()
            
            # Assign roles based on context and position
            if len(price_values) >= 1:
                prices['current'] = price_values[-1]  # Usually the last/most recent price
            
            if len(price_values) >= 2:
                prices['high'] = max(price_values)
                prices['low'] = min(price_values)
            
            if len(price_values) >= 4:
                # Try to identify OHLC
                prices['open'] = price_values[0]
                prices['close'] = price_values[-1]
            
            logger.info(f"Extracted {len(price_values)} price values")
        
        return prices
    
    def _extract_current_price(self, text: str, prices: Dict[str, float]) -> float:
        """Extract the current/latest price"""
        if 'current' in prices:
            return prices['current']
        
        if 'close' in prices:
            return prices['close']
        
        # Look for price patterns near keywords
        current_patterns = [
            r'(?:PRICE|CURRENT|NOW|LAST)[\s:]*(\d+\.\d{2,5})',
            r'(\d+\.\d{2,5})[\s]*(?:CURRENT|NOW|LAST)'
        ]
        
        for pattern in current_patterns:
            match = re.search(pattern, text.upper())
            if match:
                try:
                    price = float(match.group(1))
                    logger.info(f"Found current price: {price}")
                    return price
                except ValueError:
                    continue
        
        # If no specific current price found, return 0
        return 0.0
    
    def _extract_metadata(self, text: str) -> Dict[str, Any]:
        """Extract additional metadata from text"""
        metadata = {}
        
        # Look for spread information
        spread_match = re.search(r'SPREAD[\s:]*(\d+\.?\d*)', text.upper())
        if spread_match:
            metadata['spread'] = float(spread_match.group(1))
        
        # Look for volume information
        volume_match = re.search(r'VOLUME[\s:]*(\d+)', text.upper())
        if volume_match:
            metadata['volume'] = int(volume_match.group(1))
        
        # Look for date/time information
        date_patterns = [
            r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',
            r'\d{1,2}:\d{2}(?::\d{2})?'
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            if matches:
                metadata['timestamps'] = matches
                break
        
        return metadata
    
    def _calculate_confidence(self, symbol: str, timeframe: str, prices: Dict[str, float]) -> float:
        """Calculate confidence score for OCR extraction"""
        confidence = 0.0
        
        # Symbol confidence
        if symbol != "UNKNOWN":
            confidence += 0.4
        
        # Timeframe confidence
        if timeframe != "UNKNOWN":
            confidence += 0.3
        
        # Price data confidence
        if prices:
            confidence += 0.3 * min(len(prices) / 4, 1.0)  # Max 0.3 for having 4+ prices
        
        return round(confidence, 2)
