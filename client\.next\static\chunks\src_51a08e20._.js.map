{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/MultiStepAnalysis.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { \n  Upload, \n  X, \n  Image as ImageIcon, \n  Loader2, \n  CheckCircle, \n  AlertCircle,\n  Eye,\n  Edit3,\n  ArrowRight,\n  RefreshCw\n} from 'lucide-react';\nimport axios from 'axios';\n\ninterface MultiStepAnalysisProps {\n  onAnalysisComplete: (data: any) => void;\n  onReset: () => void;\n}\n\ntype AnalysisStep = 'upload' | 'reading' | 'summary' | 'correction' | 'analyzing' | 'complete';\n\ninterface InitialSummary {\n  summary: string;\n  ocr_data: {\n    symbol: string;\n    timeframe: string;\n    current_price: number;\n    extracted_text: string;\n    price_count: number;\n  };\n  basic_patterns: {\n    trend: string;\n    pattern: string;\n    price_range: {\n      high: number;\n      low: number;\n    };\n  };\n}\n\nexport default function MultiStepAnalysis({ \n  onAnalysisComplete, \n  onReset \n}: MultiStepAnalysisProps) {\n  const [uploadedFile, setUploadedFile] = useState<File | null>(null);\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [currentStep, setCurrentStep] = useState<AnalysisStep>('upload');\n  const [uploadId, setUploadId] = useState<string | null>(null);\n  const [initialSummary, setInitialSummary] = useState<InitialSummary | null>(null);\n  const [userCorrection, setUserCorrection] = useState<string>('');\n  const [errorMessage, setErrorMessage] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      setUploadedFile(file);\n      setPreviewUrl(URL.createObjectURL(file));\n      setCurrentStep('upload');\n      setErrorMessage('');\n      setInitialSummary(null);\n      setUserCorrection('');\n    }\n  }, []);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp']\n    },\n    maxFiles: 1,\n    maxSize: 10 * 1024 * 1024, // 10MB\n  });\n\n  const handleStartAnalysis = async () => {\n    if (!uploadedFile) return;\n\n    setCurrentStep('reading');\n    setErrorMessage('');\n\n    try {\n      // Step 1: Upload file\n      const formData = new FormData();\n      formData.append('image', uploadedFile);\n\n      const uploadResponse = await axios.post('http://localhost:5000/api/upload', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (uploadResponse.data.success) {\n        const newUploadId = uploadResponse.data.data.id;\n        setUploadId(newUploadId);\n\n        // Step 2: Start initial reading\n        const readingResponse = await axios.post(`http://localhost:5000/api/analysis/initial-read/${newUploadId}`);\n\n        if (readingResponse.data.success) {\n          // Poll for initial reading results\n          pollForInitialReading(newUploadId);\n        } else {\n          throw new Error('Gagal memulai pembacaan gambar');\n        }\n      } else {\n        throw new Error('Upload gagal');\n      }\n    } catch (error: any) {\n      console.error('Error starting analysis:', error);\n      setCurrentStep('upload');\n      setErrorMessage(error.response?.data?.message || error.message || 'Terjadi kesalahan');\n    }\n  };\n\n  const pollForInitialReading = async (uploadId: string) => {\n    const maxAttempts = 30; // 2.5 minutes with 5-second intervals\n    let attempts = 0;\n\n    const poll = async () => {\n      try {\n        attempts++;\n        const response = await axios.get(`http://localhost:5000/api/analysis/initial-read/${uploadId}`);\n\n        if (response.data.success && response.data.data) {\n          // Initial reading completed\n          setInitialSummary(response.data.data);\n          setCurrentStep('summary');\n        } else if (response.data.status === 'processing') {\n          // Still processing, continue polling\n          if (attempts < maxAttempts) {\n            setTimeout(poll, 5000);\n          } else {\n            throw new Error('Timeout: Pembacaan gambar terlalu lama');\n          }\n        } else {\n          throw new Error('Gagal membaca gambar');\n        }\n      } catch (error: any) {\n        console.error('Polling error:', error);\n        setCurrentStep('upload');\n        setErrorMessage(error.message || 'Terjadi kesalahan saat membaca gambar');\n      }\n    };\n\n    poll();\n  };\n\n  const handleConfirmSummary = () => {\n    setCurrentStep('analyzing');\n    startFinalAnalysis('');\n  };\n\n  const handleRequestCorrection = () => {\n    setCurrentStep('correction');\n  };\n\n  const handleSubmitCorrection = () => {\n    if (!userCorrection.trim()) {\n      setErrorMessage('Mohon masukkan koreksi atau konteks tambahan');\n      return;\n    }\n    \n    setCurrentStep('analyzing');\n    startFinalAnalysis(userCorrection);\n  };\n\n  const startFinalAnalysis = async (userPrompt: string) => {\n    if (!uploadId) return;\n\n    try {\n      const analysisResponse = await axios.post(`http://localhost:5000/api/analysis/final-analyze/${uploadId}`, {\n        user_prompt: userPrompt\n      });\n\n      if (analysisResponse.data.success) {\n        // Poll for final results\n        pollForFinalResults(uploadId);\n      } else {\n        throw new Error('Gagal memulai analisis final');\n      }\n    } catch (error: any) {\n      console.error('Final analysis error:', error);\n      setCurrentStep('summary');\n      setErrorMessage(error.response?.data?.message || error.message || 'Gagal memulai analisis');\n    }\n  };\n\n  const pollForFinalResults = async (uploadId: string) => {\n    const maxAttempts = 60; // 5 minutes with 5-second intervals\n    let attempts = 0;\n\n    const poll = async () => {\n      try {\n        attempts++;\n        const response = await axios.get(`http://localhost:5000/api/analysis/upload/${uploadId}`);\n\n        if (response.data.success && response.data.data) {\n          // Analysis completed\n          setCurrentStep('complete');\n          onAnalysisComplete(response.data.data);\n        } else if (response.data.status === 'processing') {\n          // Still processing, continue polling\n          if (attempts < maxAttempts) {\n            setTimeout(poll, 5000);\n          } else {\n            throw new Error('Timeout: Analisis terlalu lama');\n          }\n        } else {\n          throw new Error('Analisis gagal');\n        }\n      } catch (error: any) {\n        console.error('Final polling error:', error);\n        setCurrentStep('summary');\n        setErrorMessage(error.message || 'Terjadi kesalahan saat analisis');\n      }\n    };\n\n    poll();\n  };\n\n  const handleReset = () => {\n    setUploadedFile(null);\n    setPreviewUrl(null);\n    setCurrentStep('upload');\n    setUploadId(null);\n    setInitialSummary(null);\n    setUserCorrection('');\n    setErrorMessage('');\n    onReset();\n  };\n\n  const renderStepIndicator = () => {\n    const steps = [\n      { key: 'upload', label: 'Upload', icon: Upload },\n      { key: 'reading', label: 'Membaca', icon: Eye },\n      { key: 'summary', label: 'Ringkasan', icon: CheckCircle },\n      { key: 'analyzing', label: 'Analisis', icon: RefreshCw },\n      { key: 'complete', label: 'Selesai', icon: CheckCircle }\n    ];\n\n    const stepIndex = steps.findIndex(step => step.key === currentStep);\n\n    return (\n      <div className=\"flex items-center justify-center mb-8\">\n        {steps.map((step, index) => {\n          const Icon = step.icon;\n          const isActive = index <= stepIndex;\n          const isCurrent = index === stepIndex;\n\n          return (\n            <div key={step.key} className=\"flex items-center\">\n              <div className={`\n                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300\n                ${isActive \n                  ? 'bg-blue-500 border-blue-500 text-white' \n                  : 'bg-gray-100 border-gray-300 text-gray-400'\n                }\n                ${isCurrent && (currentStep === 'reading' || currentStep === 'analyzing') \n                  ? 'animate-pulse' \n                  : ''\n                }\n              `}>\n                <Icon size={16} />\n              </div>\n              \n              <span className={`\n                ml-2 text-sm font-medium transition-colors duration-300\n                ${isActive ? 'text-blue-600' : 'text-gray-400'}\n              `}>\n                {step.label}\n              </span>\n\n              {index < steps.length - 1 && (\n                <ArrowRight \n                  size={16} \n                  className={`mx-4 transition-colors duration-300 ${\n                    isActive ? 'text-blue-400' : 'text-gray-300'\n                  }`} \n                />\n              )}\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto p-6\">\n      {renderStepIndicator()}\n\n      {errorMessage && (\n        <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n          <AlertCircle className=\"text-red-500 mr-3\" size={20} />\n          <span className=\"text-red-700\">{errorMessage}</span>\n        </div>\n      )}\n\n      {/* Upload Step */}\n      {currentStep === 'upload' && (\n        <div className=\"space-y-6\">\n          <div\n            {...getRootProps()}\n            className={`\n              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-300\n              ${isDragActive \n                ? 'border-blue-400 bg-blue-50' \n                : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'\n              }\n            `}\n          >\n            <input {...getInputProps()} />\n            <div className=\"flex flex-col items-center\">\n              <Upload className=\"text-gray-400 mb-4\" size={48} />\n              <p className=\"text-lg font-medium text-gray-700 mb-2\">\n                {isDragActive ? 'Lepaskan file di sini...' : 'Drag & drop gambar chart atau klik untuk pilih'}\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                Mendukung PNG, JPG, JPEG, GIF, BMP, WebP (max 10MB)\n              </p>\n            </div>\n          </div>\n\n          {previewUrl && (\n            <div className=\"space-y-4\">\n              <div className=\"relative\">\n                <img\n                  src={previewUrl}\n                  alt=\"Preview\"\n                  className=\"w-full max-h-96 object-contain rounded-lg border border-gray-200\"\n                />\n                <button\n                  onClick={() => {\n                    setUploadedFile(null);\n                    setPreviewUrl(null);\n                  }}\n                  className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n                >\n                  <X size={16} />\n                </button>\n              </div>\n\n              <button\n                onClick={handleStartAnalysis}\n                className=\"w-full bg-blue-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-600 transition-colors flex items-center justify-center\"\n              >\n                <Eye className=\"mr-2\" size={20} />\n                Mulai Analisis Chart\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Reading Step */}\n      {currentStep === 'reading' && (\n        <div className=\"text-center space-y-6\">\n          <div className=\"flex flex-col items-center\">\n            <div className=\"relative\">\n              <Loader2 className=\"animate-spin text-blue-500 mb-4\" size={48} />\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <Eye className=\"text-blue-600\" size={24} />\n              </div>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\n              Sedang Membaca Chart...\n            </h3>\n            <p className=\"text-gray-600\">\n              Sistem sedang menganalisis dan membaca informasi dari gambar chart Anda\n            </p>\n          </div>\n\n          {previewUrl && (\n            <div className=\"max-w-md mx-auto\">\n              <img\n                src={previewUrl}\n                alt=\"Chart being analyzed\"\n                className=\"w-full rounded-lg border border-gray-200 opacity-75\"\n              />\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Summary Step */}\n      {currentStep === 'summary' && initialSummary && (\n        <div className=\"space-y-6\">\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <h3 className=\"text-xl font-semibold text-blue-800 mb-4 flex items-center\">\n              <CheckCircle className=\"mr-2\" size={24} />\n              Hasil Pembacaan Chart\n            </h3>\n\n            <div className=\"space-y-4\">\n              <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n                <h4 className=\"font-medium text-gray-800 mb-2\">Ringkasan:</h4>\n                <p className=\"text-gray-700 whitespace-pre-line leading-relaxed\">\n                  {initialSummary.summary}\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n                  <h4 className=\"font-medium text-gray-800 mb-2\">Informasi Dasar:</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600\">\n                    <li><strong>Symbol:</strong> {initialSummary.ocr_data.symbol}</li>\n                    <li><strong>Timeframe:</strong> {initialSummary.ocr_data.timeframe}</li>\n                    <li><strong>Current Price:</strong> {initialSummary.ocr_data.current_price.toFixed(5)}</li>\n                    <li><strong>Data Points:</strong> {initialSummary.ocr_data.price_count}</li>\n                  </ul>\n                </div>\n\n                <div className=\"bg-white rounded-lg p-4 border border-blue-100\">\n                  <h4 className=\"font-medium text-gray-800 mb-2\">Pattern Analysis:</h4>\n                  <ul className=\"space-y-1 text-sm text-gray-600\">\n                    <li><strong>Trend:</strong> {initialSummary.basic_patterns.trend}</li>\n                    <li><strong>Pattern:</strong> {initialSummary.basic_patterns.pattern}</li>\n                    <li><strong>High:</strong> {initialSummary.basic_patterns.price_range.high.toFixed(5)}</li>\n                    <li><strong>Low:</strong> {initialSummary.basic_patterns.price_range.low.toFixed(5)}</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <button\n              onClick={handleConfirmSummary}\n              className=\"flex-1 bg-green-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-green-600 transition-colors flex items-center justify-center\"\n            >\n              <CheckCircle className=\"mr-2\" size={20} />\n              Ya, Informasi Sudah Benar\n            </button>\n\n            <button\n              onClick={handleRequestCorrection}\n              className=\"flex-1 bg-orange-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-orange-600 transition-colors flex items-center justify-center\"\n            >\n              <Edit3 className=\"mr-2\" size={20} />\n              Perlu Koreksi\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Correction Step */}\n      {currentStep === 'correction' && (\n        <div className=\"space-y-6\">\n          <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-6\">\n            <h3 className=\"text-xl font-semibold text-orange-800 mb-4 flex items-center\">\n              <Edit3 className=\"mr-2\" size={24} />\n              Koreksi & Konteks Tambahan\n            </h3>\n\n            <p className=\"text-orange-700 mb-4\">\n              Mohon berikan koreksi atau informasi tambahan yang sesuai dengan chart Anda:\n            </p>\n\n            <textarea\n              value={userCorrection}\n              onChange={(e) => setUserCorrection(e.target.value)}\n              placeholder=\"Contoh: Symbol seharusnya EURUSD, timeframe H1, trend saat ini bullish, ada pattern double bottom di area support...\"\n              className=\"w-full h-32 p-4 border border-orange-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n            />\n\n            <div className=\"mt-4 text-sm text-orange-600\">\n              <p><strong>Tips:</strong></p>\n              <ul className=\"list-disc list-inside space-y-1 mt-2\">\n                <li>Sebutkan symbol yang benar (contoh: EURUSD, XAUUSD)</li>\n                <li>Koreksi timeframe jika salah (M1, M5, M15, M30, H1, H4, D1)</li>\n                <li>Jelaskan trend yang Anda lihat (bullish, bearish, sideways)</li>\n                <li>Sebutkan pattern khusus yang Anda identifikasi</li>\n                <li>Berikan konteks market condition saat ini</li>\n              </ul>\n            </div>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <button\n              onClick={() => setCurrentStep('summary')}\n              className=\"flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-600 transition-colors\"\n            >\n              Kembali\n            </button>\n\n            <button\n              onClick={handleSubmitCorrection}\n              disabled={!userCorrection.trim()}\n              className=\"flex-1 bg-blue-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center\"\n            >\n              <ArrowRight className=\"mr-2\" size={20} />\n              Lanjutkan Analisis\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Analyzing Step */}\n      {currentStep === 'analyzing' && (\n        <div className=\"text-center space-y-6\">\n          <div className=\"flex flex-col items-center\">\n            <div className=\"relative\">\n              <Loader2 className=\"animate-spin text-blue-500 mb-4\" size={48} />\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <RefreshCw className=\"text-blue-600\" size={24} />\n              </div>\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\n              Sedang Menganalisis Chart...\n            </h3>\n            <p className=\"text-gray-600 mb-4\">\n              Sistem sedang melakukan analisis mendalam berdasarkan informasi yang telah dikonfirmasi\n            </p>\n\n            {userCorrection && (\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md\">\n                <p className=\"text-sm text-blue-700\">\n                  <strong>Konteks tambahan:</strong> {userCorrection}\n                </p>\n              </div>\n            )}\n          </div>\n\n          {previewUrl && (\n            <div className=\"max-w-md mx-auto\">\n              <img\n                src={previewUrl}\n                alt=\"Chart being analyzed\"\n                className=\"w-full rounded-lg border border-gray-200 opacity-75\"\n              />\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Reset button for all steps except upload */}\n      {currentStep !== 'upload' && currentStep !== 'complete' && (\n        <div className=\"mt-8 text-center\">\n          <button\n            onClick={handleReset}\n            className=\"text-gray-500 hover:text-gray-700 transition-colors text-sm\"\n          >\n            Mulai Ulang\n          </button>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAhBA;;;;;AA4Ce,SAAS,kBAAkB,KAGjB;QAHiB,EACxC,kBAAkB,EAClB,OAAO,EACgB,GAHiB;;IAIxC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEzD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC1B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,MAAM;gBACR,gBAAgB;gBAChB,cAAc,IAAI,eAAe,CAAC;gBAClC,eAAe;gBACf,gBAAgB;gBAChB,kBAAkB;gBAClB,kBAAkB;YACpB;QACF;gDAAG,EAAE;IAEL,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAQ;gBAAQ;aAAQ;QAC/D;QACA,UAAU;QACV,SAAS,KAAK,OAAO;IACvB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;QAEnB,eAAe;QACf,gBAAgB;QAEhB,IAAI;YACF,sBAAsB;YACtB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,MAAM,iBAAiB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,oCAAoC,UAAU;gBACpF,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE;gBAC/B,MAAM,cAAc,eAAe,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC/C,YAAY;gBAEZ,gCAAgC;gBAChC,MAAM,kBAAkB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,mDAA8D,OAAZ;gBAE5F,IAAI,gBAAgB,IAAI,CAAC,OAAO,EAAE;oBAChC,mCAAmC;oBACnC,sBAAsB;gBACxB,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;gBAGH,sBAAA;YAFhB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,eAAe;YACf,gBAAgB,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI,MAAM,OAAO,IAAI;QACpE;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,MAAM,cAAc,IAAI,sCAAsC;QAC9D,IAAI,WAAW;QAEf,MAAM,OAAO;YACX,IAAI;gBACF;gBACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,mDAA2D,OAAT;gBAEpF,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC/C,4BAA4B;oBAC5B,kBAAkB,SAAS,IAAI,CAAC,IAAI;oBACpC,eAAe;gBACjB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,cAAc;oBAChD,qCAAqC;oBACrC,IAAI,WAAW,aAAa;wBAC1B,WAAW,MAAM;oBACnB,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,eAAe;gBACf,gBAAgB,MAAM,OAAO,IAAI;YACnC;QACF;QAEA;IACF;IAEA,MAAM,uBAAuB;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,0BAA0B;QAC9B,eAAe;IACjB;IAEA,MAAM,yBAAyB;QAC7B,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,gBAAgB;YAChB;QACF;QAEA,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,mBAAmB,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,AAAC,oDAA4D,OAAT,WAAY;gBACxG,aAAa;YACf;YAEA,IAAI,iBAAiB,IAAI,CAAC,OAAO,EAAE;gBACjC,yBAAyB;gBACzB,oBAAoB;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAY;gBAGH,sBAAA;YAFhB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe;YACf,gBAAgB,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI,MAAM,OAAO,IAAI;QACpE;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,MAAM,cAAc,IAAI,oCAAoC;QAC5D,IAAI,WAAW;QAEf,MAAM,OAAO;YACX,IAAI;gBACF;gBACA,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,AAAC,6CAAqD,OAAT;gBAE9E,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC/C,qBAAqB;oBACrB,eAAe;oBACf,mBAAmB,SAAS,IAAI,CAAC,IAAI;gBACvC,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,cAAc;oBAChD,qCAAqC;oBACrC,IAAI,WAAW,aAAa;wBAC1B,WAAW,MAAM;oBACnB,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,eAAe;gBACf,gBAAgB,MAAM,OAAO,IAAI;YACnC;QACF;QAEA;IACF;IAEA,MAAM,cAAc;QAClB,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,QAAQ;YACZ;gBAAE,KAAK;gBAAU,OAAO;gBAAU,MAAM,yMAAA,CAAA,SAAM;YAAC;YAC/C;gBAAE,KAAK;gBAAW,OAAO;gBAAW,MAAM,mMAAA,CAAA,MAAG;YAAC;YAC9C;gBAAE,KAAK;gBAAW,OAAO;gBAAa,MAAM,8NAAA,CAAA,cAAW;YAAC;YACxD;gBAAE,KAAK;gBAAa,OAAO;gBAAY,MAAM,mNAAA,CAAA,YAAS;YAAC;YACvD;gBAAE,KAAK;gBAAY,OAAO;gBAAW,MAAM,8NAAA,CAAA,cAAW;YAAC;SACxD;QAED,MAAM,YAAY,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;QAEvD,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,OAAO,KAAK,IAAI;gBACtB,MAAM,WAAW,SAAS;gBAC1B,MAAM,YAAY,UAAU;gBAE5B,qBACE,6LAAC;oBAAmB,WAAU;;sCAC5B,6LAAC;4BAAI,WAAW,AAAC,mIAMb,OAJA,WACE,2CACA,6CACH,sBAIA,OAHC,aAAa,CAAC,gBAAgB,aAAa,gBAAgB,WAAW,IACpE,kBACA,IACH;sCAED,cAAA,6LAAC;gCAAK,MAAM;;;;;;;;;;;sCAGd,6LAAC;4BAAK,WAAW,AAAC,8FAE+B,OAA7C,WAAW,kBAAkB,iBAAgB;sCAE9C,KAAK,KAAK;;;;;;wBAGZ,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC,qNAAA,CAAA,aAAU;4BACT,MAAM;4BACN,WAAW,AAAC,uCAEX,OADC,WAAW,kBAAkB;;;;;;;mBA1B3B,KAAK,GAAG;;;;;YAgCtB;;;;;;IAGN;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ;YAEA,8BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;wBAAoB,MAAM;;;;;;kCACjD,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;YAKnC,gBAAgB,0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACE,GAAG,cAAc;wBAClB,WAAW,AAAC,+HAKT,OAHC,eACE,+BACA,0DACH;;0CAGH,6LAAC;gCAAO,GAAG,eAAe;;;;;;0CAC1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;wCAAqB,MAAM;;;;;;kDAC7C,6LAAC;wCAAE,WAAU;kDACV,eAAe,6BAA6B;;;;;;kDAE/C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAMxC,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,SAAS;4CACP,gBAAgB;4CAChB,cAAc;wCAChB;wCACA,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;YAS3C,gBAAgB,2BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;wCAAkC,MAAM;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;4CAAgB,MAAM;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;oBAK9B,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;YAQnB,gBAAgB,aAAa,gCAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;0DACV,eAAe,OAAO;;;;;;;;;;;;kDAI3B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAgB;oEAAE,eAAe,QAAQ,CAAC,MAAM;;;;;;;0EAC5D,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAmB;oEAAE,eAAe,QAAQ,CAAC,SAAS;;;;;;;0EAClE,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAuB;oEAAE,eAAe,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;;;;;;;0EACnF,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAqB;oEAAE,eAAe,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;0DAI1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAe;oEAAE,eAAe,cAAc,CAAC,KAAK;;;;;;;0EAChE,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAiB;oEAAE,eAAe,cAAc,CAAC,OAAO;;;;;;;0EACpE,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAc;oEAAE,eAAe,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;;;;;;;0EACnF,6LAAC;;kFAAG,6LAAC;kFAAO;;;;;;oEAAa;oEAAE,eAAe,cAAc,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;0CAI5C,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,QAAK;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;YAQ3C,gBAAgB,8BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,6MAAA,CAAA,QAAK;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;0CAItC,6LAAC;gCAAE,WAAU;0CAAuB;;;;;;0CAIpC,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,aAAY;gCACZ,WAAU;;;;;;0CAGZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAE,cAAA,6LAAC;sDAAO;;;;;;;;;;;kDACX,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAKV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAID,6LAAC;gCACC,SAAS;gCACT,UAAU,CAAC,eAAe,IAAI;gCAC9B,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;wCAAO,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;YAQhD,gBAAgB,6BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;wCAAkC,MAAM;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;4CAAgB,MAAM;;;;;;;;;;;;;;;;;0CAG/C,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;4BAIjC,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;sDACX,6LAAC;sDAAO;;;;;;wCAA0B;wCAAE;;;;;;;;;;;;;;;;;;oBAM3C,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;YAQnB,gBAAgB,YAAY,gBAAgB,4BAC3C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAOX;GA3fwB;;QAwBgC,2KAAA,CAAA,cAAW;;;KAxB3C", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/AnalysisResults.tsx"], "sourcesContent": ["'use client';\n\nimport { TrendingUp, TrendingDown, Minus, Target, Shield, DollarSign, BarChart3, Loader2 } from 'lucide-react';\n\ninterface AnalysisResultsProps {\n  data: any;\n  isAnalyzing: boolean;\n}\n\nexport default function AnalysisResults({ data, isAnalyzing }: AnalysisResultsProps) {\n  if (isAnalyzing) {\n    return (\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <div className=\"flex items-center justify-center py-12\">\n          <div className=\"text-center\">\n            <Loader2 className=\"w-12 h-12 text-blue-500 animate-spin mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n              Menganalisis Chart...\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-300\">\n              Sedang memproses Price Action, SMC, dan Technical Indicators\n            </p>\n            <div className=\"mt-4 flex justify-center space-x-2\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!data) {\n    return (\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <div className=\"text-center py-12\">\n          <BarChart3 className=\"w-16 h-16 text-slate-300 dark:text-slate-600 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n            Hasil Analisis\n          </h3>\n          <p className=\"text-slate-600 dark:text-slate-300\">\n            Upload chart trading untuk melihat analisis mendalam\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  // Parse results if it's a string\n  let results;\n  try {\n    results = typeof data.results === 'string' ? JSON.parse(data.results) : data.results;\n  } catch (error) {\n    results = data.results || {};\n  }\n\n  const recommendation = data.recommendation || results?.decision?.recommendation || 'HOLD';\n  const confidence = data.confidence || results?.decision?.confidence || 0;\n  const entryPrice = data.entry_price || results?.decision?.entry_price || 0;\n  const stopLoss = data.stop_loss || results?.decision?.stop_loss || 0;\n  const takeProfit = data.take_profit || results?.decision?.take_profit || 0;\n  const riskReward = data.risk_reward_ratio || results?.decision?.risk_reward_ratio || 0;\n\n  const getRecommendationIcon = () => {\n    switch (recommendation) {\n      case 'BUY':\n        return <TrendingUp className=\"w-6 h-6 text-green-500\" />;\n      case 'SELL':\n        return <TrendingDown className=\"w-6 h-6 text-red-500\" />;\n      default:\n        return <Minus className=\"w-6 h-6 text-yellow-500\" />;\n    }\n  };\n\n  const getRecommendationColor = () => {\n    switch (recommendation) {\n      case 'BUY':\n        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';\n      case 'SELL':\n        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';\n      default:\n        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';\n    }\n  };\n\n  const getConfidenceColor = () => {\n    if (confidence >= 0.8) return 'text-green-600 dark:text-green-400';\n    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';\n    return 'text-red-600 dark:text-red-400';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Recommendation */}\n      <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n        <h2 className=\"text-2xl font-semibold text-slate-900 dark:text-white mb-6\">\n          Rekomendasi Trading\n        </h2>\n        \n        <div className=\"flex items-center justify-between mb-6\">\n          <div className={`flex items-center space-x-3 px-4 py-3 rounded-xl ${getRecommendationColor()}`}>\n            {getRecommendationIcon()}\n            <span className=\"text-2xl font-bold\">{recommendation}</span>\n          </div>\n          \n          <div className=\"text-right\">\n            <div className=\"text-sm text-slate-500 dark:text-slate-400\">Confidence</div>\n            <div className={`text-2xl font-bold ${getConfidenceColor()}`}>\n              {(confidence * 100).toFixed(1)}%\n            </div>\n          </div>\n        </div>\n\n        {/* Price Levels */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <DollarSign className=\"w-4 h-4 text-blue-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Entry Price</span>\n            </div>\n            <div className=\"text-xl font-bold text-slate-900 dark:text-white\">\n              {entryPrice > 0 ? entryPrice.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Shield className=\"w-4 h-4 text-red-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Stop Loss</span>\n            </div>\n            <div className=\"text-xl font-bold text-red-600 dark:text-red-400\">\n              {stopLoss > 0 ? stopLoss.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n\n          <div className=\"bg-slate-50 dark:bg-slate-700 rounded-xl p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Target className=\"w-4 h-4 text-green-500\" />\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Take Profit</span>\n            </div>\n            <div className=\"text-xl font-bold text-green-600 dark:text-green-400\">\n              {takeProfit > 0 ? takeProfit.toFixed(4) : 'N/A'}\n            </div>\n          </div>\n        </div>\n\n        {/* Risk Reward */}\n        {riskReward > 0 && (\n          <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-slate-600 dark:text-slate-300\">Risk-Reward Ratio</span>\n              <span className=\"text-lg font-bold text-blue-600 dark:text-blue-400\">\n                1:{riskReward.toFixed(2)}\n              </span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Analysis Details */}\n      {results && (\n        <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n          <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-4\">\n            Detail Analisis\n          </h3>\n          \n          <div className=\"space-y-4\">\n            {/* Price Action */}\n            {results.price_action && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Price Action</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Trend: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.price_action.trend || 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Signal: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.price_action.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* SMC */}\n            {results.smc && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Smart Money Concept</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Structure: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.smc.market_structure || 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Signal: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.smc.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Technical Indicators */}\n            {results.technical && (\n              <div className=\"p-4 bg-slate-50 dark:bg-slate-700 rounded-xl\">\n                <h4 className=\"font-semibold text-slate-900 dark:text-white mb-2\">Technical Indicators</h4>\n                <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">RSI: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.technical.rsi?.value ? results.technical.rsi.value.toFixed(2) : 'N/A'}\n                    </span>\n                  </div>\n                  <div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">MACD: </span>\n                    <span className=\"font-medium text-slate-900 dark:text-white\">\n                      {results.technical.macd?.signal || 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AASe,SAAS,gBAAgB,KAA2C;QAA3C,EAAE,IAAI,EAAE,WAAW,EAAwB,GAA3C;QAgDQ,mBACR,oBACC,oBACJ,oBACI,oBACM,oBA4JxB,wBAMA;IAtNrB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAGlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;8CACjG,6LAAC;oCAAI,WAAU;oCAAkD,OAAO;wCAAE,gBAAgB;oCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM7G;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAM1D;IAEA,iCAAiC;IACjC,IAAI;IACJ,IAAI;QACF,UAAU,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,KAAK,CAAC,KAAK,OAAO,IAAI,KAAK,OAAO;IACtF,EAAE,OAAO,OAAO;QACd,UAAU,KAAK,OAAO,IAAI,CAAC;IAC7B;IAEA,MAAM,iBAAiB,KAAK,cAAc,KAAI,oBAAA,+BAAA,oBAAA,QAAS,QAAQ,cAAjB,wCAAA,kBAAmB,cAAc,KAAI;IACnF,MAAM,aAAa,KAAK,UAAU,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,UAAU,KAAI;IACvE,MAAM,aAAa,KAAK,WAAW,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,WAAW,KAAI;IACzE,MAAM,WAAW,KAAK,SAAS,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,SAAS,KAAI;IACnE,MAAM,aAAa,KAAK,WAAW,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,WAAW,KAAI;IACzE,MAAM,aAAa,KAAK,iBAAiB,KAAI,oBAAA,+BAAA,qBAAA,QAAS,QAAQ,cAAjB,yCAAA,mBAAmB,iBAAiB,KAAI;IAErF,MAAM,wBAAwB;QAC5B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,cAAc,KAAK,OAAO;QAC9B,IAAI,cAAc,KAAK,OAAO;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,oDAA4E,OAAzB;;oCACjE;kDACD,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6C;;;;;;kDAC5D,6LAAC;wCAAI,WAAW,AAAC,sBAA0C,OAArB;;4CACnC,CAAC,aAAa,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,WAAW,IAAI,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAyD;;;;;;;;;;;;kDAE3E,6LAAC;wCAAI,WAAU;kDACZ,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;oBAM/C,aAAa,mBACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAyD;;;;;;8CACzE,6LAAC;oCAAK,WAAU;;wCAAqD;wCAChE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;YAQ/B,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,6LAAC;wBAAI,WAAU;;4BAEZ,QAAQ,YAAY,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,YAAY,CAAC,KAAK,IAAI;;;;;;;;;;;;0DAGnC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,YAAY,CAAC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;4BAQzC,QAAQ,GAAG,kBACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,GAAG,CAAC,gBAAgB,IAAI;;;;;;;;;;;;0DAGrC,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,GAAG,CAAC,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;4BAQhC,QAAQ,SAAS,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,EAAA,yBAAA,QAAQ,SAAS,CAAC,GAAG,cAArB,6CAAA,uBAAuB,KAAK,IAAG,QAAQ,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAG7E,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;kEACrD,6LAAC;wDAAK,WAAU;kEACb,EAAA,0BAAA,QAAQ,SAAS,CAAC,IAAI,cAAtB,8CAAA,wBAAwB,MAAM,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD;KAlOwB", "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>u, X, TrendingUp, BarChart3 } from 'lucide-react';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-slate-900 dark:text-white\">\n                Market Analysis\n              </h1>\n              <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                AI-Powered Trading Insights\n              </p>\n            </div>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              Dashboard\n            </a>\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              History\n            </a>\n            <a \n              href=\"#\" \n              className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n            >\n              Settings\n            </a>\n          </nav>\n\n          {/* Stats */}\n          <div className=\"hidden lg:flex items-center space-x-6\">\n            <div className=\"flex items-center space-x-2\">\n              <BarChart3 className=\"w-4 h-4 text-green-500\" />\n              <div className=\"text-sm\">\n                <div className=\"text-slate-900 dark:text-white font-semibold\">98.5%</div>\n                <div className=\"text-slate-500 dark:text-slate-400 text-xs\">Accuracy</div>\n              </div>\n            </div>\n            <div className=\"w-px h-8 bg-slate-200 dark:bg-slate-700\"></div>\n            <div className=\"text-sm\">\n              <div className=\"text-slate-900 dark:text-white font-semibold\">1,247</div>\n              <div className=\"text-slate-500 dark:text-slate-400 text-xs\">Analyses</div>\n            </div>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors\"\n          >\n            {isMenuOpen ? (\n              <X className=\"w-6 h-6 text-slate-600 dark:text-slate-300\" />\n            ) : (\n              <Menu className=\"w-6 h-6 text-slate-600 dark:text-slate-300\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-slate-200 dark:border-slate-700\">\n            <nav className=\"flex flex-col space-y-4\">\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                Dashboard\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                History\n              </a>\n              <a \n                href=\"#\" \n                className=\"text-slate-600 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium\"\n              >\n                Settings\n              </a>\n              \n              {/* Mobile Stats */}\n              <div className=\"flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700\">\n                <div className=\"flex items-center space-x-2\">\n                  <BarChart3 className=\"w-4 h-4 text-green-500\" />\n                  <div className=\"text-sm\">\n                    <span className=\"text-slate-900 dark:text-white font-semibold\">98.5% </span>\n                    <span className=\"text-slate-500 dark:text-slate-400\">Accuracy</span>\n                  </div>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-slate-900 dark:text-white font-semibold\">1,247 </span>\n                  <span className=\"text-slate-500 dark:text-slate-400\">Analyses</span>\n                </div>\n              </div>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAO9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA+C;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA+C;;;;;;sDAC9D,6LAAC;4CAAI,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAKhE,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BACC,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAMrB,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;0CAKD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA+C;;;;;;kEAC/D,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;kDAGzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvE;GApHwB;KAAA", "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Vicky/project%20baru/auto-analysis/client/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport MultiStepAnalysis from '@/components/MultiStepAnalysis';\nimport AnalysisResults from '@/components/AnalysisResults';\nimport Header from '@/components/Header';\n\nexport default function Home() {\n  const [analysisData, setAnalysisData] = useState(null);\n\n  const handleAnalysisComplete = (data: any) => {\n    setAnalysisData(data);\n  };\n\n  const handleReset = () => {\n    setAnalysisData(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800\">\n      <Header />\n\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Hero Section */}\n          <div className=\"text-center mb-12\">\n            <h1 className=\"text-4xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6\">\n              Market Trend\n              <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                {' '}Analysis\n              </span>\n            </h1>\n            <p className=\"text-xl text-slate-600 dark:text-slate-300 max-w-3xl mx-auto leading-relaxed\">\n              Upload screenshot chart trading Anda dan dapatkan analisis mendalam menggunakan\n              Price Action, Smart Money Concept, dan Technical Indicators untuk rekomendasi trading yang akurat.\n            </p>\n          </div>\n\n          {/* Main Content */}\n          <div className=\"grid lg:grid-cols-2 gap-8\">\n            {/* Upload Section */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n                <h2 className=\"text-2xl font-semibold text-slate-900 dark:text-white mb-4\">\n                  Analisis Chart Trading Interaktif\n                </h2>\n                <MultiStepAnalysis\n                  onAnalysisComplete={handleAnalysisComplete}\n                  onReset={handleReset}\n                />\n              </div>\n\n              {/* Features */}\n              <div className=\"bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 border border-slate-200 dark:border-slate-700\">\n                <h3 className=\"text-xl font-semibold text-slate-900 dark:text-white mb-4\">\n                  Fitur Analisis\n                </h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Price Action Analysis</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Smart Money Concept</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">Technical Indicators</span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                    <span className=\"text-slate-600 dark:text-slate-300\">OCR Text Recognition</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Results Section */}\n            <div className=\"space-y-6\">\n              <AnalysisResults\n                data={analysisData}\n                isAnalyzing={false}\n              />\n            </div>\n          </div>\n\n          {/* Info Cards */}\n          <div className=\"grid md:grid-cols-3 gap-6 mt-12\">\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Analisis Komprehensif\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Kombinasi Price Action, SMC, dan Technical Indicators untuk analisis yang mendalam dan akurat.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Rekomendasi Cepat\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Dapatkan rekomendasi BUY/SELL/HOLD dengan confidence level dan risk management yang jelas.\n              </p>\n            </div>\n\n            <div className=\"bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700\">\n              <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-4\">\n                <svg className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-lg font-semibold text-slate-900 dark:text-white mb-2\">\n                Risk Management\n              </h3>\n              <p className=\"text-slate-600 dark:text-slate-300 text-sm\">\n                Stop Loss, Take Profit, dan Risk-Reward Ratio yang dihitung otomatis untuk trading yang aman.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,yBAAyB,CAAC;QAC9B,gBAAgB;IAClB;IAEA,MAAM,cAAc;QAClB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;;;;;0BAEP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqE;sDAEjF,6LAAC;4CAAK,WAAU;;gDACb;gDAAI;;;;;;;;;;;;;8CAGT,6LAAC;oCAAE,WAAU;8CAA+E;;;;;;;;;;;;sCAO9F,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA6D;;;;;;8DAG3E,6LAAC,0IAAA,CAAA,UAAiB;oDAChB,oBAAoB;oDACpB,SAAS;;;;;;;;;;;;sDAKb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;sEAEvD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wIAAA,CAAA,UAAe;wCACd,MAAM;wCACN,aAAa;;;;;;;;;;;;;;;;;sCAMnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA2C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAClG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA6C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACpG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAA+C,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE;GAhIwB;KAAA", "debugId": null}}]}